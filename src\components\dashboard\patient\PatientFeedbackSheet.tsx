import { Sheet, SheetContent } from "@/components/ui/sheet";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { X, Clock, User, Stethoscope } from "lucide-react";
import { Star } from "lucide-react";
import type {
	DetailedPatientFeedback,
	DetailedFeedbackResponse,
} from "@/lib/api/patientExperienceApi";

interface PatientFeedbackSheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	feedbackData?: {
		patientName: string;
		patientInitials: string;
		date: string;
		rating: number;
		service: string;
		doctor: string;
		location: string;
		questions: Array<{
			id: string;
			question: string;
			answer: string;
		}>;
		feedback?: string;
	};
	detailedFeedback?: DetailedPatientFeedback | null;
	isLoading?: boolean;
}

const defaultFeedbackData = {
	patientName: "Sophia Anderson",
	patientInitials: "SA",
	date: "Mar 12, 2025 , 2:00 PM",
	rating: 4,
	service: "Skin Check-up",
	doctor: "Dr. <PERSON>",
	location: "Dermatology Services",
	questions: [
		{
			id: "1",
			question: "I found booking my appointment online easy.",
			answer: "Strongly Agree",
		},
		{
			id: "2",
			question:
				"I was able to select an appointment time that was convenient for me.",
			answer: "Agree",
		},
		{
			id: "3",
			question: "I found booking my appointment online easy.",
			answer: "Strongly Agree",
		},
		{
			id: "4",
			question:
				"I was able to select an appointment time that was convenient for me.",
			answer: "Agree",
		},
	],
	feedback:
		"Request a Position Swap for Critical Situations. Request a Position Swap for Critical Situations. Request a Position Swap for Critical Situations. Request a Position Swap for Critical Situations.",
};

export function PatientFeedbackSheet({
	open,
	onOpenChange,
	feedbackData = defaultFeedbackData,
	detailedFeedback,
	isLoading = false,
}: PatientFeedbackSheetProps) {
	const handleClose = () => {
		onOpenChange(false);
	};

	const renderStars = (rating: number) => {
		return Array.from({ length: 5 }, (_, index) => (
			<Star
				key={index}
				className={`h-4 w-4 ${
					index < rating
						? "fill-amber-400 text-amber-400"
						: "fill-gray-300 text-gray-300"
				}`}
			/>
		));
	};

	const formatDate = (dateString: string): string => {
		if (!dateString || typeof dateString !== "string") {
			return "N/A";
		}
		try {
			const date = new Date(dateString);
			if (isNaN(date.getTime())) {
				return dateString;
			}
			return date.toLocaleDateString("en-US", {
				day: "2-digit",
				month: "short",
				year: "numeric",
				hour: "2-digit",
				minute: "2-digit",
				hour12: true,
			});
		} catch {
			return dateString || "N/A";
		}
	};

	const getPatientInitials = (name: string): string => {
		if (!name || typeof name !== "string") {
			return "N/A";
		}
		return name
			.split(" ")
			.map((word) => word.charAt(0).toUpperCase())
			.join("")
			.slice(0, 2);
	};

	const formatResponseValue = (
		response: DetailedFeedbackResponse
	): string => {
		// Handle different response types and formats
		if (
			response.value_type === "array" &&
			Array.isArray(response.raw_value)
		) {
			return response.raw_value.join(", ");
		}
		return (
			response.formatted_value ||
			response.raw_value?.toString() ||
			"No response"
		);
	};

	// Use detailed feedback data if available, otherwise fall back to basic feedbackData
	const displayData = detailedFeedback
		? {
				patientName: detailedFeedback.patient_name,
				patientInitials: getPatientInitials(
					detailedFeedback.patient_name
				),
				patientAvatar: detailedFeedback.patient_avatar,
				date: formatDate(detailedFeedback.date),
				rating: parseFloat(detailedFeedback.rating) || 0,
				service: detailedFeedback.service_name || "Unknown Service",
				location: detailedFeedback.location_name,
				stationName: detailedFeedback.station_name,
				generalFeedback: detailedFeedback.general_feedback,
				responses: detailedFeedback.responses || [],
			}
		: feedbackData;

	if (isLoading) {
		return (
			<Sheet open={open} onOpenChange={onOpenChange}>
				<SheetContent className="w-full !max-w-[600px] overflow-y-auto p-4 [&>button]:hidden">
					<div className="flex h-64 items-center justify-center">
						<div className="text-gray-500">
							Loading feedback details...
						</div>
					</div>
				</SheetContent>
			</Sheet>
		);
	}

	return (
		<Sheet open={open} onOpenChange={onOpenChange}>
			<SheetContent className="w-full !max-w-[600px] overflow-y-auto p-4 [&>button]:hidden">
				<div className="flex flex-col items-start justify-start gap-4">
					<div className="inline-flex w-full items-center justify-between">
						<div className="flex flex-1 items-center justify-start gap-3">
							<Avatar className="h-9 w-9 rounded-full">
								{detailedFeedback?.patient_avatar ? (
									<AvatarImage
										src={detailedFeedback.patient_avatar}
										alt={displayData.patientName}
									/>
								) : (
									<AvatarFallback className="bg-gray-100 text-base font-semibold">
										{displayData.patientInitials}
									</AvatarFallback>
								)}
							</Avatar>
							<div className="text-base font-semibold text-gray-900">
								{displayData.patientName}
							</div>
						</div>
						<Button
							variant="ghost"
							size="icon"
							onClick={handleClose}
							className="h-4 w-4 p-0"
						>
							<X className="h-4 w-4" />
						</Button>
					</div>
					<div className="inline-flex items-center justify-start gap-4">
						<div className="flex items-center justify-start gap-1">
							<Clock className="h-3 w-3 text-gray-500" />
							<div className="text-xs font-normal text-gray-900">
								{displayData.date}
							</div>
						</div>
						<div className="flex items-center justify-start gap-1">
							{renderStars(displayData.rating)}
						</div>
					</div>
					<div className="h-px w-full border-t border-gray-200" />

					{/* Location and Station Info */}
					<div className="inline-flex w-full items-start justify-start gap-2.5">
						<div className="flex flex-1 items-center justify-start gap-1">
							<Stethoscope className="h-3 w-3 text-gray-500" />
							<div className="text-xs font-normal text-gray-900">
								{detailedFeedback
									? displayData.location
									: feedbackData.location}
							</div>
						</div>
						{detailedFeedback ? (
							<div className="flex flex-1 items-center justify-start gap-1">
								<User className="h-3 w-3 text-gray-500" />
								<div className="text-xs font-normal text-gray-900">
									{displayData?.stationName}
								</div>
							</div>
						) : (
							<div className="flex flex-1 items-center justify-start gap-1">
								<User className="h-3 w-3 text-gray-500" />
								<div className="text-xs font-normal text-gray-900">
									{feedbackData.doctor}
								</div>
							</div>
						)}
					</div>

					{/* Service */}
					<div className="flex w-full flex-col items-start justify-center gap-1.5">
						<div className="text-[8px] leading-3 font-normal text-gray-500">
							Service
						</div>
						<div className="text-xs leading-none font-normal text-gray-900">
							{displayData.service}
						</div>
					</div>

					<div className="h-px w-full rounded-full bg-gray-200" />

					{/* Survey Responses */}
					{detailedFeedback
						? displayData.responses.map((response, index) => (
								<div
									key={response.field_id || index}
									className="flex w-full flex-col items-start justify-start gap-1.5"
								>
									<div className="w-full text-xs leading-none font-normal text-gray-900">
										{response.question}
									</div>
									<div className="inline-flex w-full flex-wrap content-start items-start justify-start gap-3">
										<div className="flex items-center justify-center gap-2.5 rounded-md bg-gray-100 px-2 py-1">
											<div className="text-[10px] leading-3 font-medium text-gray-900">
												{formatResponseValue(response)}
											</div>
										</div>
									</div>
									<div className="text-[8px] leading-3 font-normal text-gray-400">
										Type: {response.display_type}
									</div>
								</div>
							))
						: feedbackData.questions.map((item) => (
								<div
									key={item.id}
									className="flex w-full flex-col items-start justify-start gap-1.5"
								>
									<div className="w-full text-xs leading-none font-normal text-gray-900">
										{item.question}
									</div>
									<div className="inline-flex w-full flex-wrap content-start items-start justify-start gap-3">
										<div className="flex items-center justify-center gap-2.5 rounded-md bg-gray-100 px-2 py-1">
											<div className="text-[10px] leading-3 font-medium text-gray-900">
												{item.answer}
											</div>
										</div>
									</div>
								</div>
							))}

					<div className="h-px w-full rounded-full bg-gray-200" />

					{/* General Feedback */}
					{(detailedFeedback?.general_feedback ||
						feedbackData.feedback) && (
						<div className="flex w-full flex-col items-start justify-center gap-1.5">
							<div className="text-[8px] leading-3 font-normal text-gray-500">
								General Feedback
							</div>
							<div className="w-full text-xs leading-none font-normal text-gray-900">
								{detailedFeedback?.general_feedback ||
									feedbackData.feedback}
							</div>
						</div>
					)}
				</div>
			</SheetContent>
		</Sheet>
	);
}
