import { Input } from "@/components/ui/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	// FormMessage,
} from "@/components/ui/form";
import { Checkbox } from "@/components/common/Checkbox";
import { useEffect } from "react";

export const DefaultValuesOption = ({
	sectionIndex,
	field,
	control,
	watch,
	form,
}: {
	sectionIndex: number;
	field: any;
	control: any;
	watch: any;
	form: any;
}) => {
	const getQuestions = (
		currentSectionIndex: number,
		currentFieldIndex: number
	) => {
		const sections = watch("sections") || [];
		return sections.flatMap((section: any, sIndex: number) =>
			section.fields
				.map((field: any, fIndex: number) => ({
					id: field.id,
					title: field.title || "Untitled Field",
					sectionIndex: sIndex,
					fieldIndex: fIndex,
				}))
				.filter((field: any) => {
					const fieldData = watch(
						`sections.${field.sectionIndex}.fields.${field.fieldIndex}`
					);
					return (
						(fieldData.type === "radio" ||
							fieldData.type === "dropdown" ||
							fieldData.type === "yes_no") &&
						(field.sectionIndex < currentSectionIndex ||
							(field.sectionIndex === currentSectionIndex &&
								field.fieldIndex < currentFieldIndex))
					);
				})
		);
	};

	const getOptions = (fieldId: string) => {
		if (!fieldId) return [];

		const sections = watch("sections") || [];
		for (const section of sections) {
			const foundField = section.fields.find(
				(f: any) => f.id === fieldId
			);
			if (foundField) {
				return foundField.options || [];
			}
		}
		return [];
	};

	// Watch for reference field changes and populate default_values
	const referenceFieldId = watch(
		`sections.${sectionIndex}.fields.${field.name}.reference_field_id`
	);

	const addPredefinedValue =
		watch(
			`sections.${sectionIndex}.fields.${field.name}.add_predefined_value`
		) || false;

	useEffect(() => {
		if (referenceFieldId && addPredefinedValue) {
			const referenceOptions = getOptions(referenceFieldId);
			if (referenceOptions.length > 0) {
				const defaultValues = referenceOptions.map(() => ({
					reference_field_id: referenceFieldId,
					default_value: "",
					allow_edit: true,
				}));

				form.setValue(
					`sections.${sectionIndex}.fields.${field.name}.default_values`,
					defaultValues
				);
			}
		}
	}, [referenceFieldId, addPredefinedValue, form, sectionIndex, field.name]);

	if (
		field.value.type !== "text" &&
		field.value.type !== "longtext" &&
		field.value.type !== "numeric" &&
		field.value.type !== "dropdown" &&
		field.value.type !== "radio" &&
		field.value.type !== "yes_no"
	)
		return null;

	return (
		<div className="mt-4 space-y-6 border-t pt-2.5">
			<FormField
				control={control}
				name={`sections.${sectionIndex}.fields.${field.name}.add_predefined_value`}
				render={({ field: typeField }) => (
					<FormItem className="ml-5 flex items-center gap-2">
						<Checkbox
							checked={typeField.value}
							onCheckedChange={typeField.onChange}
							id={typeField.name}
						/>
						<FormLabel
							className="text-xs leading-none font-medium"
							htmlFor={typeField.name}
						>
							Add Predefined Values
						</FormLabel>
					</FormItem>
				)}
			/>
			{addPredefinedValue && (
				<>
					<FormField
						control={control}
						name={`sections.${sectionIndex}.fields.${field.name}.reference_field_id`}
						render={({ field: refField }) => (
							<FormItem className="ml-5">
								<FormLabel className="text-xs">
									Select Reference Question
								</FormLabel>
								<Select
									onValueChange={refField.onChange}
									value={refField.value || ""}
								>
									<SelectTrigger className="w-full text-xs placeholder:text-xs">
										<SelectValue placeholder="Select reference question" />
									</SelectTrigger>
									<SelectContent>
										{getQuestions(
											sectionIndex,
											field.name
										).map((question: any) => (
											<SelectItem
												key={question.id}
												value={question.id}
											>
												{`Section ${question.sectionIndex + 1}, Question ${question.fieldIndex + 1}: ${question.title}`}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</FormItem>
						)}
					/>
					<div className="bg-foreground-subtle space-y-4 rounded-lg p-5 pr-3">
						{referenceFieldId &&
							getOptions(referenceFieldId).map(
								(option: any, index: number) => (
									<>
										<FormField
											key={option.id}
											control={control}
											name={`sections.${sectionIndex}.fields.${field.name}.default_values.${index}.default_value`}
											render={({
												field: defaultValueField,
											}) => (
												<FormItem>
													<FormLabel className="text-xs">
														Option {index + 1}:{" "}
														{option.value}
													</FormLabel>
													<FormControl>
														<Input
															type="text"
															placeholder="Enter your default value here"
															{...defaultValueField}
															value={
																defaultValueField.value ||
																""
															}
															className="text-xs placeholder:text-xs"
														/>
													</FormControl>
												</FormItem>
											)}
										/>
										<FormField
											control={control}
											name={`sections.${sectionIndex}.fields.${field.name}.default_values.${index}.allow_edit`}
											render={({ field: typeField }) => (
												<FormItem className="flex items-center justify-end gap-2">
													<Checkbox
														checked={
															typeField.value
														}
														onCheckedChange={
															typeField.onChange
														}
														id={typeField.name}
													/>
													<FormLabel
														className="text-xs leading-none font-medium"
														htmlFor={typeField.name}
													>
														Allow Edit
													</FormLabel>
												</FormItem>
											)}
										/>
									</>
								)
							)}
					</div>
				</>
			)}
		</div>
	);
};
