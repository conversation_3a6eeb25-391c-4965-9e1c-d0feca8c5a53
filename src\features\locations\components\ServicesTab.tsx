import { useEffect, useState } from "react";
import { Search, Plus, MapPin, Upload, Settings2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { InputText } from "@/components/common/InputText";
import { DataTable, type Column } from "@/components/common/DataTable";
import {
	AddServiceSheet,
	ServiceFilterSheet,
	ServiceInfoSheet,
} from "./sheets";
import { useServices } from "../hooks/useServices";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import { useDeleteActions } from "@/hooks/useDeleteActions";
import type { ServiceData } from "../api/servicesApi";
import LocationDetailsSheet from "./sheets/location-details/LocationDetailsSheet";
import { ServiceCard } from "./service/ServiceCard";
import { useDebounce } from "@/hooks/useDebounce";

interface ServicesTabProps {
	className?: string;
	onRefetchReady?: (refetchFn: () => void) => void;
}

export function ServicesTab({ className, onRefetchReady }: ServicesTabProps) {
	const { organizationId } = useOrganizationContext();
	const { deleteService } = useDeleteActions();
	const [selectedServices, setSelectedServices] = useState<string[]>([]);
	const [searchTerm, setSearchTerm] = useState("");
	const [appliedFilters, setAppliedFilters] = useState<Record<string, any>>(
		{}
	);
	const [showAddServiceForm, setShowAddServiceForm] = useState(false);
	const [showFilterSheet, setShowFilterSheet] = useState(false);
	const [showServiceInfo, setShowServiceInfo] = useState(false);
	const [showLocationDetails, setShowLocationDetails] = useState(false);
	const [selectedService, setSelectedService] = useState<ServiceData | null>(
		null
	);

	// Debounce search term to avoid too many API calls
	const debouncedSearchTerm = useDebounce(searchTerm, 300);

	// Combine search and filters for API call
	const apiFilters = {
		...appliedFilters,
		...(debouncedSearchTerm && { search: debouncedSearchTerm }),
	};

	// Fetch services using React Query with filters and search
	const {
		data: servicesResponse,
		isLoading,
		error: servicesError,
		refetch,
	} = useServices({
		organizationId: organizationId || undefined,
		enabled: !!organizationId,
		filters: apiFilters,
	});

	const services = servicesResponse?.data || [];

	// No need for client-side filtering since search is handled by API
	const filteredServices = services;

	// Pass refetch function to parent
	useEffect(() => {
		if (onRefetchReady && refetch) {
			onRefetchReady(refetch);
		}
	}, [onRefetchReady, refetch]);

	const handleSelectAll = (checked: boolean) => {
		if (checked && filteredServices.length > 0) {
			setSelectedServices(
				filteredServices.map((service) => service.id.toString())
			);
		} else {
			setSelectedServices([]);
		}
	};

	const handleServiceSelection = (serviceId: string, selected: boolean) => {
		if (selected) {
			setSelectedServices((prev) => [...prev, serviceId]);
		} else {
			setSelectedServices((prev) =>
				prev.filter((id) => id !== serviceId)
			);
		}
	};

	const handleAddService = async (data?: any) => {
		// Refetch services after adding a new one
		await refetch();
		setShowAddServiceForm(false);
	};

	const handleViewService = (service: ServiceData) => {
		setSelectedService(service);
		setShowServiceInfo(true);
	};

	const handleEditService = (service: ServiceData) => {
		setSelectedService(service);
		setShowServiceInfo(false);
		// You can open an edit form here
		console.log("Edit service:", service);
	};

	const handleDeleteService = (service: ServiceData) => {
		deleteService(service, {
			refetch: async () => {
				await refetch();
			},
		});
	};

	const handleApplyFilters = (filterData: any) => {
		console.log("Applying filters:", filterData);

		// Transform filter data to API query parameters
		const apiFilters: Record<string, any> = {};

		// Add location filters
		if (filterData.locations && filterData.locations.length > 0) {
			apiFilters.location = filterData.locations;
		}

		// Add provider/station filters
		if (filterData.providers && filterData.providers.length > 0) {
			apiFilters.station = filterData.providers;
		}

		// Add method filters
		if (
			filterData.availableMethods &&
			filterData.availableMethods.length > 0
		) {
			apiFilters.methods = filterData.availableMethods;
		}

		// Add status filters
		if (filterData.status && filterData.status.length > 0) {
			apiFilters.status = filterData.status;
		}

		// Add sorting (default to name)
		apiFilters.sort_by = "name";

		// Add date range filters if needed
		// apiFilters.from = "2023-01-01";
		// apiFilters.to = "2023-12-31";

		console.log("Transformed API filters:", apiFilters);
		setAppliedFilters(apiFilters);
	};

	const handleClearFilters = () => {
		setAppliedFilters({});
		setSearchTerm("");
	};

	// Check if any filters or search are applied
	const hasActiveFilters =
		Object.keys(appliedFilters).length > 0 || debouncedSearchTerm;

	// Define table columns
	const columns: Column<ServiceData>[] = [
		{
			key: "name",
			label: "Service Name",
			width: "flex-2",
		},
		{
			key: "status",
			label: "Status",
			width: "flex-1",
		},
		{
			key: "forms",
			label: "Forms",
			width: "flex-1",
		},
		{
			key: "autoApprove",
			label: "Auto Approve",
			width: "flex-1",
		},
		{
			key: "time",
			label: "Time",
			width: "flex-1",
		},
		{
			key: "actions",
			label: "",
			width: "flex-1",
		},
	];

	return (
		<div className={className}>
			{/* Header */}
			<div className="flex items-center justify-between py-3 pl-4">
				<h1 className="text-2xl font-bold">Services</h1>
				<div className="flex items-center gap-3">
					<div className="relative max-w-md flex-1">
						<InputText
							placeholder="Search"
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="pl-10 focus-visible:ring-0"
							id="search-field"
							variant="with-icon"
							icon={<Search className="h-4 w-4" />}
							iconPosition="left"
						/>
					</div>
					<Button
						variant="outline"
						className={`cursor-pointer ${
							hasActiveFilters
								? "border-primary bg-primary/10 text-primary"
								: ""
						}`}
						size="icon"
						onClick={() => setShowFilterSheet(true)}
					>
						<Settings2 className="h-4 w-4" />
					</Button>
					{hasActiveFilters && (
						<Button
							variant="ghost"
							size="sm"
							onClick={handleClearFilters}
							className="text-gray-500 hover:text-gray-700"
						>
							Clear Filters
						</Button>
					)}
					<Button
						variant="outline"
						className="cursor-pointer"
						onClick={() => setShowAddServiceForm(true)}
					>
						<Upload className="mr-2 h-4 w-4" />
						Import CSV
					</Button>
					<Button
						variant="outline"
						className="bg-primary hover:bg-primary/90 cursor-pointer text-white hover:text-white"
						onClick={() => setShowAddServiceForm(true)}
					>
						<Plus className="mr-2 h-4 w-4" />
						Add a Service
					</Button>
				</div>
			</div>

			{/* Table */}
			<DataTable
				columns={columns}
				data={filteredServices}
				isLoading={isLoading}
				error={servicesError}
				selectedItems={selectedServices}
				onSelectAll={handleSelectAll}
				onItemSelect={handleServiceSelection}
				getItemId={(service) => service.id.toString()}
				renderItem={(service, isSelected, onSelect) => (
					<ServiceCard
						key={service.id}
						service={service}
						isSelected={isSelected}
						onSelectionChange={onSelect}
						onEdit={() => console.log("Edit service:", service.id)}
						onView={() => handleViewService(service)}
						onDelete={() => handleDeleteService(service)}
					/>
				)}
				emptyState={{
					icon: <MapPin className="mx-auto h-12 w-12 text-gray-400" />,
					title: "No service found",
					description: "Get started by creating your first service.",
					action: {
						label: "Add a Service",
						onClick: () => setShowAddServiceForm(true),
					},
				}}
			/>

			{/* Add Service Sheet */}
			<AddServiceSheet
				open={showAddServiceForm}
				onOpenChange={setShowAddServiceForm}
				onSubmit={handleAddService}
			/>

			{/* Service Details Sheet */}
			<LocationDetailsSheet
				open={showLocationDetails}
				onClose={() => setShowLocationDetails(false)}
			/>

			{/* Service Info Sheet */}
			<ServiceInfoSheet
				open={showServiceInfo}
				onOpenChange={setShowServiceInfo}
				service={selectedService}
				onEdit={handleEditService}
			/>

			{/* Filter Sheet */}
			<ServiceFilterSheet
				open={showFilterSheet}
				onOpenChange={setShowFilterSheet}
				onApplyFilters={handleApplyFilters}
			/>
		</div>
	);
}
