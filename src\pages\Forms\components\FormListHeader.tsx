import { type FC } from "react";
import { Checkbox } from "@/components/common/Checkbox";
import * as Types from "../types";

export interface FormListHeaderProps {
	selectedForms: string[];
	formResponse: Types.FormTypes.GetFormsResponse;
	handleSelectAll: (selected: boolean) => void;
}

export const FormListHeader: FC<FormListHeaderProps> = ({
	selectedForms,
	formResponse,
	handleSelectAll,
}) => {
	return (
		<div className="text-muted flex h-12 items-center justify-between border-b py-1 pl-4 text-xs">
			<div className="flex items-center pr-4">
				<Checkbox
					label=""
					checked={
						selectedForms.length === formResponse?.data?.length &&
						formResponse?.data?.length > 0
					}
					className="cursor-pointer"
					onCheckedChange={handleSelectAll}
				/>
			</div>
			<div className="flex flex-2 items-center px-3">
				<div className="flex items-center gap-3">
					<p className="text-nowrap">Form Name</p>
				</div>
			</div>
			<div className="flex flex-1 items-center px-3">
				<div className="flex items-center gap-3">
					<p className="text-nowrap">Type</p>
				</div>
			</div>
			<div className="flex flex-1 items-center px-3">
				<div className="flex items-center gap-3">
					<p className="text-nowrap">Service</p>
				</div>
			</div>
			<div className="flex flex-1 items-center px-3">
				<div className="flex items-center gap-3">
					<p className="text-nowrap">Providers</p>
				</div>
			</div>
			<div className="flex flex-1 items-center px-3">
				<div className="flex items-center gap-3">
					<p className="text-nowrap">Status</p>
				</div>
			</div>
			<div className="flex flex-1 items-center px-3">
				<div className="flex items-center gap-3">
					<p className="text-nowrap">Created At</p>
				</div>
			</div>
			<div className="flex min-w-[140px] flex-1 items-center px-3">
				<div className="flex items-center gap-3">
					<p></p>
				</div>
			</div>
		</div>
	);
};
