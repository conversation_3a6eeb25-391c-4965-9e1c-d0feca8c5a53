import type { ReactNode } from "react";

export interface UploadedFile {
	id: string;
	name: string;
	size: number;
	type: string;
	url?: string;
	preview?: string;
	localPreview?: string; // For local file preview before upload
	uploadStatus?: "pending" | "uploading" | "success" | "error";
	uploadError?: string;
}

export interface UploaderProps {
	/**
	 * Current uploaded files
	 */
	files?: UploadedFile[];
	/**
	 * Whether the uploader is in loading state
	 */
	isLoading?: boolean;
	/**
	 * Whether multiple files can be uploaded
	 */
	multiple?: boolean;
	/**
	 * Accepted file types
	 */
	accept?: string;
	/**
	 * Maximum file size in bytes
	 */
	maxFileSize?: number;
	/**
	 * Maximum number of files
	 */
	maxFiles?: number;
	/**
	 * Whether the uploader is disabled
	 */
	disabled?: boolean;
	/**
	 * Custom upload text
	 */
	uploadText?: string;
	/**
	 * Custom description text
	 */
	descriptionText?: string;
	/**
	 * Size variant
	 */
	size?: "sm" | "md" | "lg" | "default";
	/**
	 * Visual variant
	 */
	variant?: "default" | "compact" | "bordered";
	/**
	 * Custom className
	 */
	className?: string;
	/**
	 * Custom icon for upload area
	 */
	uploadIcon?: ReactNode;
	/**
	 * Enable automatic server upload when files are selected
	 */
	enableServerUpload?: boolean;
	/**
	 * Callback when files are selected/dropped
	 */
	onFilesChange?: (files: File[]) => void;
	/**
	 * Callback when a file is removed
	 */
	onFileRemove?: (fileId: string) => void;
	/**
	 * Callback for upload error
	 */
	onError?: (error: string) => void;
	/**
	 * Callback when server upload starts for a file
	 */
	onUploadStart?: (fileId: string) => void;
	/**
	 * Callback when server upload succeeds for a file
	 */
	onUploadSuccess?: (file: UploadedFile, uploadedUrl: string) => void;
	/**
	 * Callback when server upload fails for a file
	 */
	onUploadError?: (fileId: string, error: string) => void;
}

export interface FileItemProps {
	file: UploadedFile;
	onRemove?: (fileId: string) => void;
	onChange?: (fileId: string, newFile: File) => void;
	accept?: string;
	className?: string;
}
