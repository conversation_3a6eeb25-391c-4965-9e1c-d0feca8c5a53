import { useMemo } from 'react'
import { Position, useNodeId } from 'reactflow'
import CustomHandle from './custom-handle'
import clsx from 'clsx'
import { useEditor } from '../provider/editor'
import type { EditorCanvasCardType, EditorCanvasTypes } from '../libs/type'
import { Play } from 'lucide-react'
import { PiGitForkBold } from 'react-icons/pi'
import { LuCopy, LuMousePointerClick, LuSearchCheck, LuTrash } from 'react-icons/lu'
import { Button } from '@/components/ui/Button/Button'
import { memo } from 'react'

const iconToRender = (type: EditorCanvasTypes) => {
    switch (type) {
        case 'Trigger':
            return <Play color="#005893" size={20} />
        case 'Condition':
            return <PiGitForkBold color="#B45309" size={20} />
        case 'IF Match':
            return <LuMousePointerClick color="#289144" size={20} />
        case 'Else Match':
            return <LuMousePointerClick color="#289144" size={20} />
        case 'Check':
            return <LuSearchCheck color="#228579" size={20} />
    }
}

// Move ConditionMetaDisplay to top-level
const ConditionMetaDisplay = memo(function ConditionMetaDisplay({ conditionMetadata }: { conditionMetadata: any }) {
    // Collect all possible metadata fields to display
    const metaItems: { label: string, values: string[] }[] = [];
    if (conditionMetadata?.category && Array.isArray(conditionMetadata.category) && conditionMetadata.category.length > 0) {
        metaItems.push({
            label: "Category",
            values: conditionMetadata.category
        });
    }
    if (conditionMetadata?.provider && Array.isArray(conditionMetadata.provider) && conditionMetadata.provider.length > 0) {
        metaItems.push({
            label: "Provider",
            values: conditionMetadata.provider
        });
    }
    // If you want to add more meta fields, add them here (e.g. service, location, etc.)

    // DateTime is a special case, not an array
    let hasDateTime = !!(conditionMetadata?.dateTime?.before || conditionMetadata?.dateTime?.after);

    // Registration label
    let registrationLabel = "";
    if (conditionMetadata?.registration === "registered") {
        registrationLabel = "Registered";
    } else if (conditionMetadata?.registration === "not_registered") {
        registrationLabel = "Not Registered";
    } else if (conditionMetadata?.registration === "all") {
        registrationLabel = "All";
    }

    // Count how many meta "rows" will be shown (metaItems, dateTime, registration)
    // Only show up to 2 metaItems (not including dateTime and registration, which are always shown if present)
    const shownMeta = metaItems.slice(0, 1);
    const hiddenMetaCount = metaItems.length - shownMeta.length;

    // The UI can show up to 4 meta "rows" if: 2 metaItems + dateTime + registration are all present.
    // This is by design: only metaItems are limited to 2, but dateTime and registration always show if present.

    return (
        <>
            {shownMeta.map((item) => (
                <p className="text-[#71717A] text-sm font-light text-start" key={item.label}>
                    {item.label} is <span className="text-[#005893]">
                        {item.values.slice(0, 2).join(', ')}
                        {item.values.length > 2 && (
                            <> +{item.values.length - 2} more</>
                        )}
                    </span>
                </p>
            ))}
            {hasDateTime && (
                <p className="text-[#71717A] text-sm font-light text-start">
                    {conditionMetadata?.dateTime?.before && (
                        <>
                            Before <span className="text-[#005893]">
                                {conditionMetadata?.dateTime?.before?.toLocaleDateString()}
                            </span>
                            {conditionMetadata?.dateTime?.after && (
                                <> and after <span className="text-[#005893]">
                                    {conditionMetadata?.dateTime?.after?.toLocaleDateString()}
                                </span>
                                </>
                            )}
                        </>
                    )}
                    {!conditionMetadata?.dateTime?.before && conditionMetadata?.dateTime?.after && (
                        <>
                            After <span className="text-[#005893]">
                                {conditionMetadata?.dateTime?.after?.toLocaleDateString()}
                            </span>
                        </>
                    )}
                </p>
            )}
            {registrationLabel && (
                <p className="text-[#71717A] text-sm font-light text-start">
                    Registration: <span className="text-[#005893]">{registrationLabel}</span>
                </p>
            )}
            {hiddenMetaCount > 0 && (
                <div className="absolute left-3 bottom-2 text-xs text-[#005893] font-medium">
                    +{hiddenMetaCount} more
                </div>
            )}
        </>
    );
});
ConditionMetaDisplay.displayName = "ConditionMetaDisplay";

const TriggerComponent = memo(({ data }: { data: EditorCanvasCardType }) => {
    const triggerMetadata = data.metadata?.type === 'Trigger' ? data.metadata : null
    const isConfigured = triggerMetadata?.triggerType

    return (
        <div className="bg-[#F4F4F5] py-2.5 px-3.5 rounded-lg">
            {isConfigured ? (
                <p className="text-[#71717A] text-sm font-light text-start">
                    Trigger is <span className="text-[#005893]">{triggerMetadata.triggerType}</span>
                </p>
            ) : (
                <p className="text-[#71717A] text-sm font-light text-start">Select the trigger that start the automation.</p>
            )}
        </div>
    )
});
TriggerComponent.displayName = "TriggerComponent";

const CheckComponent = memo(({ data }: { data: EditorCanvasCardType }) => {
    const checkMetadata = data.metadata?.type === 'Check' ? data.metadata : null
    const isConfigured = checkMetadata?.checkItem

    return (
        <div className="bg-[#F4F4F5] py-2.5 px-3.5 rounded-lg">
            {isConfigured ? (
                <p className="text-[#71717A] text-sm font-light text-start">
                    Will check <span className="text-[#005893]">
                        {checkMetadata.checkItem.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}</span>
                    {""} every{" "}
                    <span className=" text-[#005893]">{checkMetadata.numberOfChecks} {checkMetadata.intervalUnit}</span>
                </p>
            ) : (
                <p className="text-[#71717A] text-sm font-light text-start">Select the check that will be performed.</p>
            )}
        </div>
    )
})
CheckComponent.displayName = "CheckComponent";

const ActionComponent = memo(({ data }: { data: EditorCanvasCardType }) => {
    const actionMetadata = data.metadata?.type === "IF Match" || data.metadata?.type === "Else Match" ? data.metadata : null
    const isConfigured = actionMetadata?.action

    return (
        <div className="bg-[#F4F4F5] py-2.5 px-3.5 rounded-lg">
            {isConfigured ? (
                <p className="text-[#71717A] text-sm font-light text-start">
                    Then <span className="text-[#005893]">{
                        actionMetadata.action
                            .map((action, idx, arr) => {
                                const actionText = action
                                    .split('-')
                                    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                                    .join(' ');
                                if (arr.length > 1 && idx === arr.length - 1) {
                                    return (
                                        <span key={action}>
                                            <span className="text-[#71717A]">and </span>
                                            {actionText}
                                        </span>
                                    );
                                }
                                return <span key={action}>{actionText}{arr.length > 2 && idx < arr.length - 2 ? ', ' : ' '}</span>;
                            })
                        }
                    </span>
                </p>
            ) : (
                <p className="text-[#71717A] text-sm font-light text-start">Select the action that will be performed.</p>
            )}
        </div>
    )
})
ActionComponent.displayName = "ActionComponent";

const ConditionComponent = memo(({ data }: { data: EditorCanvasCardType }) => {
    const conditionMetadata = data.metadata?.type === 'Condition' ? data.metadata : null
    const isConfigured = Boolean(conditionMetadata?.category?.length || conditionMetadata?.dateTime?.before || conditionMetadata?.registration || conditionMetadata?.provider?.length || conditionMetadata?.service?.length || conditionMetadata?.appointmentStatus?.length || conditionMetadata?.location?.length)

    return (
        <div className="bg-[#F4F4F5] py-2.5 px-3.5 rounded-lg">
            {isConfigured ? (
                <ConditionMetaDisplay conditionMetadata={conditionMetadata} />
            ) : (
                <p className="text-[#71717A] text-sm font-light text-start">Select the condition that will be checked.</p>
            )}
        </div>
    )
});
ConditionComponent.displayName = "ConditionComponent";

const EditorCanvasCardSingle = ({ data }: { data: EditorCanvasCardType }) => {
    const { dispatch, state } = useEditor()
    const nodeId = useNodeId()
    const logo = useMemo(() => {
        return iconToRender(data.type)

    }, [data])

    return (
        <>
            {data.type !== 'Trigger' && (
                <CustomHandle
                    type="target"
                    position={Position.Top}
                    style={{ zIndex: 100 }}
                />
            )}

            <button
                className={clsx(
                    "bg-white rounded-xl min-w-[280px] max-w-[280px] pt-3 pb-1.5 px-1.5",
                    state.editor.selectedNode.id === nodeId && "border-2 border-[#00589340] shadow-[0px_1px_2px_0px_#0000000F,0px_1px_3px_0px_#0000001A]"
                )}
                style={{
                    boxShadow: "0px 1px 2px 0px #0000000F, 0px 1px 3px 0px #0000001A",
                }}
                onClick={(e) => {
                    e.stopPropagation()
                    const val = state.editor.elements.find((n) => n.id === nodeId)
                    if (val)
                        dispatch({
                            type: 'SELECTED_ELEMENT',
                            payload: {
                                element: val,
                            },
                        })
                }}
            >

                <div className="flex items-center justify-between mb-4 px-2.5">
                    <div className="flex items-center gap-2">
                        {logo}

                        <span className="text-[#27272A] text-base font-normal capitalize">
                            {data.title}
                        </span>
                    </div>
                    <div className="flex items-center gap-2">
                        <Button
                            variant="outline"
                            size="icon"
                            className="h-9 w-9 rounded-lg cursor-pointer border-[#E4E4E7]"
                            onClick={(e) => {
                                e.stopPropagation();
                            }}
                        >
                            <LuTrash color="#A1A1AA" size={16} />
                        </Button>
                        <Button variant="outline" size="icon" className="h-9 w-9 rounded-lg cursor-pointer border-[#E4E4E7]">
                            <LuCopy color="#A1A1AA" size={16} />
                        </Button>
                    </div>
                </div>

                {data.type === 'Trigger' ? (
                    <TriggerComponent data={data} />
                ) : data.type === 'Condition' ? (
                    <ConditionComponent data={data} />
                ) : data.type === 'Check' ? (
                    <CheckComponent data={data} />
                ) : data.type === 'IF Match' || data.type === 'Else Match' ? (
                    <ActionComponent data={data} />
                ) : (
                    <div className="bg-[#F4F4F5] py-2.5 px-3.5 rounded-lg">
                        <p className="text-[#71717A] text-sm font-light text-start">
                            {data.description}
                        </p>
                    </div>
                )}

            </button>

            <CustomHandle
                type="source"
                position={Position.Bottom}
                id="a"
            />
        </>
    )
}

export default EditorCanvasCardSingle
