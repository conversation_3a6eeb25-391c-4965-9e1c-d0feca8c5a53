import { useMutation, useQuery } from "@tanstack/react-query";
import * as FormTypes from "../../types/Form";
import * as FormHttp from "../../http";

/**
 * Get Forms
 * @param params - Query parameters
 * @returns List of Forms
 */

export const useGetFormResponses = ({
	params,
	organizationId,
}: {
	params: FormTypes.GetFormsQueryParams;
	organizationId: number;
}) => {
	// Create a stable query key that includes all parameter values
	const queryKey = [
		"forms",
		organizationId,
		params.page,
		params.per_page,
		params.type,
		params.search,
		params.location_ids,
		params.station_ids,
		params.service_ids,
		// Add a timestamp to force refetch if needed
		JSON.stringify(params),
	];

	return useQuery({
		queryKey,
		queryFn: () => FormHttp.APIVersion3GetForms(params, organizationId),
		// Enable refetch on mount to ensure fresh data
		refetchOnMount: true,
		// Keep data fresh
		staleTime: 0,
		enabled: !!organizationId,
	});
};

/**
 * Get Form Counts for all types
 * @returns Form counts by type
 */
export const useGetFormCounts = ({
	organizationId,
}: {
	organizationId: number;
}) => {
	return useQuery({
		queryKey: ["formCounts", organizationId],
		queryFn: async () => {
			// Make parallel requests for all form types
			const [
				allForms,
				intakeForms,
				serviceForms,
				generalForms,
				feedbackForms,
			] = await Promise.all([
				FormHttp.APIVersion3GetForms(
					{ page: "1", per_page: "1" },
					organizationId
				),
				FormHttp.APIVersion3GetForms(
					{
						page: "1",
						per_page: "1",
						type: "intake",
					},
					organizationId
				),
				FormHttp.APIVersion3GetForms(
					{
						page: "1",
						per_page: "1",
						type: "service",
					},
					organizationId
				),
				FormHttp.APIVersion3GetForms(
					{
						page: "1",
						per_page: "1",
						type: "general",
					},
					organizationId
				),
				FormHttp.APIVersion3GetForms(
					{
						page: "1",
						per_page: "1",
						type: "feedback",
					},
					organizationId
				),
			]);

			return {
				all: allForms?.meta.pagination.total || 0,
				intake: intakeForms?.meta.pagination.total || 0,
				service: serviceForms?.meta.pagination.total || 0,
				general: generalForms?.meta.pagination.total || 0,
				feedback: feedbackForms?.meta.pagination.total || 0,
			};
		},
		enabled: !!organizationId,
	});
};

/**
 * Get Form
 * @param id - Form id
 * @returns Form
 */

export const useGetForm = (id: string, organizationId: number) => {
	return useQuery({
		queryKey: ["form", id, organizationId],
		queryFn: () => FormHttp.APIVersion3GetForm(id, organizationId),
		enabled: !!id && !!organizationId,
	});
};

/**
 * Create Form
 * @param data - Form data
 * @returns Form created response
 */

export const useCreateForm = () => {
	return useMutation({
		mutationFn: ({
			data,
			organizationId,
		}: {
			data: FormTypes.FormDataType;
			organizationId: number;
		}) => {
			return FormHttp.APIVersion3CreateForm(data, organizationId);
		},
	});
};

/**
 * Update Form
 * @param id - Form id
 * @param data - Form data
 * @returns Form updated response
 */

export const useUpdateForm = () => {
	return useMutation({
		mutationFn: ({
			id,
			data,
			organizationId,
		}: {
			id: string;
			data: FormTypes.FormDataType;
			organizationId: number;
		}) => FormHttp.APIVersion3UpdateForm(id, data, organizationId),
	});
};

/**
 * Delete Form
 * @param id - Form id
 * @returns Form deleted response
 */

export const useDeleteForm = () => {
	return useMutation({
		mutationFn: ({
			id,
			organizationId,
		}: {
			id: string;
			organizationId: number;
		}) => FormHttp.APIVersion3DeleteForm(id, organizationId),
	});
};
