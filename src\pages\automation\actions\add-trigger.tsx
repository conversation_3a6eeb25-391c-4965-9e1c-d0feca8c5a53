import { Button } from "@/components/ui/Button/Button";
import { X } from "lucide-react";
import clsx from "clsx";
import type { EditorCanvasCardMetaType, EditorNode } from "../libs/type";
import { type UseFormReturn } from "react-hook-form";
import type { AddTriggerSchemaType } from "../schema/add-trigger";
import { useState, useEffect } from "react";

type AddTriggerProps = {
    open: boolean;
    onClose?: () => void;
    triggerId?: string | null;
    onNext: () => void;
    onUpdateMetadata?: (metadata: EditorCanvasCardMetaType) => void;
    triggerForm: UseFormReturn<AddTriggerSchemaType>;
};

const triggers = [
    "Patient Validation",
    "Patient Sign in",
    "Appointment Flow Started",
    "Appointment Scheduled",
    "Walk in Appointment",
    "Appointment Rescheduled",
    "Appointment Cancelled",
    "Appointment Confirmed",
    "Patient Checked-in",
    "No-Show Detected",
    "Appointment In-Progress",
]

export default function AddTrigger({ open, onClose, onNext, onUpdateMetadata, triggerForm }: AddTriggerProps) {
    const selectedTriggerType = triggerForm.watch('triggerType');
    const [fullyOpen, setFullyOpen] = useState(false);

    useEffect(() => {
        if (open) {
            setTimeout(() => {
                setFullyOpen(open);
            }, 10);
        }
    }, [open]);

    const onSubmit = (data: { triggerType: string }) => {
        // Update the node metadata with selected trigger
        const updatedMetadata = {
            type: 'Trigger' as const,
            triggerType: data.triggerType,
        };

        if (onUpdateMetadata) {
            onUpdateMetadata(updatedMetadata);
        }

        // Call onNext to proceed
        onNext();
    };

    return (
        <div className={clsx("z-50 fixed top-5 right-[4%] w-[30rem] bg-white border border-[#00589340] shadow-[0px_2px_4px_-1px_#0000000F,0px_0px_6px_-1px_#0000001A] transition-transform duration-300 ease-in-out will-change-transform rounded-xl py-5", fullyOpen ? "translate-x-0" : "translate-x-[115%]")}>
            <div className="flex items-center justify-between">
                <h1 className="text-[#27272A] text-xl font-semibold ml-6">Add Trigger</h1>
                <Button
                    variant="ghost"
                    className="!px-0 w-11 h-10.5 rounded-lg cursor-pointer mr-3" onClick={onClose}>
                    <X className="text-base" color="#27272A" />
                </Button>
            </div>

            <form onSubmit={triggerForm.handleSubmit(onSubmit)} className="min-h-[85vh] h-full flex flex-col justify-between">
                <div className="flex flex-col gap-y-6 mt-8 mx-5">
                    {triggers.map((trigger) => (
                        <label htmlFor={trigger} key={trigger} className="flex items-center gap-x-2">
                            <input
                                type="radio"
                                id={trigger}
                                value={trigger}
                                className="size-3.5"
                                {...triggerForm.register('triggerType', {
                                    required: 'Please select a trigger type'
                                })}
                            />
                            <p className="text-[#27272A] font-medium text-sm">{trigger}</p>
                        </label>
                    ))}
                    {triggerForm.formState.errors.triggerType && (
                        <p className="text-red-500 text-sm">{triggerForm.formState.errors.triggerType.message}</p>
                    )}
                </div>
                <div className="flex justify-end px-4">
                    <Button
                        type="submit"
                        className="w-[8rem] cursor-pointer"
                        disabled={!selectedTriggerType}
                    >
                        Add Trigger
                    </Button>
                </div>
            </form>
        </div>
    );
}