import * as React from "react";
import { cn } from "@/lib/utils";
import { Checkbox } from "../common/Checkbox";
import {
    Pencil,
    Info,
    MessageCircleMore,
    Trash2,
    MoreHorizontal,
} from "lucide-react";

export interface Appointment {
    id: string;
    patientName: string;
    service: string;
    providerName: string;
    method: string;
    status: string;
    dateTime: string;
    endDateTime?: string;
}

export interface AppointmentAction {
    type: "edit" | "delete" | "info" | "email" | "reschedule";
    onClick: (appointment: Appointment) => void;
    disabled?: boolean;
}

export interface AllAppointmentListCardProps
    extends React.HTMLAttributes<HTMLDivElement> {
    appointment: Appointment;
    actions?: AppointmentAction[];
    showCheckbox?: boolean;
    onCheckboxChange?: (appointmentId: string, checked: boolean) => void;
    checked?: boolean;
    variant?: "default" | "compact";
}

const AllAppointmentListCard = React.forwardRef<
    HTMLDivElement,
    AllAppointmentListCardProps
>(
    (
        {
            className,
            appointment,
            actions = [],
            showCheckbox = true,
            onCheckboxChange,
            checked = false,
            variant = "default",
            ...props
        },
        ref
    ) => {
        const defaultActions: AppointmentAction[] = [
            {
                type: "delete",
                onClick: (appointment) => console.log("Delete", appointment.id),
            },
            {
                type: "edit",
                onClick: (appointment) => console.log("Edit", appointment.id),
            },
            {
                type: "info",
                onClick: (appointment) => console.log("Info", appointment.id),
            },
            {
                type: "email",
                onClick: (appointment) =>
                    console.log("Email", appointment.patientName),
            },
        ];

        const finalActions = actions.length > 0 ? actions : defaultActions;

        const getActionIcon = (type: AppointmentAction["type"]) => {
            switch (type) {
                case "edit":
                    return <Pencil className="h-3 w-3 text-gray-500" />;
                case "info":
                    return <Info className="h-3 w-3 text-gray-500" />;
                case "email":
                    return (
                        <MessageCircleMore className="h-3 w-3 text-gray-500" />
                    );
                case "delete":
                    return <Trash2 className="h-3 w-3 text-gray-500" />;
                case "reschedule":
                    return <MoreHorizontal className="h-3 w-3 text-gray-500" />;
                default:
                    return <MoreHorizontal className="h-3 w-3 text-gray-500" />;
            }
        };

        const getInitials = (name: string) => {
            return name
                .split(" ")
                .map((n) => n[0])
                .join("")
                .toUpperCase();
        };

        const getStatusStyle = (status: string) => {
            switch (status.toLowerCase()) {
                case "upcoming":
                    return "bg-blue-50 text-blue-700";
                case "in progress":
                    return "bg-yellow-50 text-yellow-700";
                case "completed":
                    return "bg-green-50 text-green-700";
                case "no show":
                    return "bg-red-50 text-red-700";
                case "cancelled (patient)":
                    return "bg-purple-50 text-purple-700";
                case "cancelled (admin)":
                    return "bg-gray-50 text-gray-700";
                default:
                    return "bg-gray-50 text-gray-700";
            }
        };

        const getMethodStyle = (method: string) => {
            switch (method.toLowerCase()) {
                case "in person":
                    return "bg-blue-50 text-blue-700";
                case "virtual":
                    return "bg-green-50 text-green-700";
                case "phone":
                    return "bg-orange-50 text-orange-700";
                default:
                    return "bg-gray-50 text-gray-700";
            }
        };

        return (
            <div
                ref={ref}
                className={cn(
                    "flex items-center border-t border-gray-200 transition-colors",
                    variant === "default" ? "h-16" : "h-12",
                    className
                )}
                {...props}
            >
                {showCheckbox && (
                    <div className="flex items-center px-4 flex-shrink-0">
                        <Checkbox
                            checked={checked}
                            onCheckedChange={(isChecked) =>
                                onCheckboxChange?.(appointment.id, isChecked)
                            }
                            className="border-[#005893]"
                        />
                    </div>
                )}

                {/* Patient Name */}
                <div className="flex w-80 sm:w-72 md:w-80 min-w-32 items-center gap-3 px-3">
                    <div className="flex h-9 w-9 flex-shrink-0 items-center justify-center overflow-hidden rounded-full bg-gray-100">
                        <div className="text-xs font-medium text-gray-600">
                            {getInitials(appointment.patientName)}
                        </div>
                    </div>
                    <div className="flex flex-1 items-center gap-1 min-w-0">
                        <div className="text-sm font-medium text-gray-900 truncate">
                            {appointment.patientName}
                        </div>
                    </div>
                </div>

                {/* Service */}
                <div className="flex w-56 sm:w-48 md:w-56 min-w-28 items-center px-3">
                    <div className="text-sm text-gray-900 truncate">
                        {appointment.service}
                    </div>
                </div>

                {/* Provider Name */}
                <div className="flex w-48 sm:w-40 md:w-48 min-w-24 items-center px-3">
                    <div className="text-sm text-gray-900 truncate">
                        {appointment.providerName}
                    </div>
                </div>

                {/* Method */}
                <div className="flex w-36 sm:w-28 md:w-36 min-w-20 items-center px-3">
                    <div
                        className={cn(
                            "flex items-center rounded-md px-2 py-1 flex-shrink-0",
                            getMethodStyle(appointment.method)
                        )}
                    >
                        <div className="text-[10px] font-medium whitespace-nowrap">
                            {appointment.method}
                        </div>
                    </div>
                </div>

                {/* Status */}
                <div className="flex w-44 sm:w-36 md:w-44 min-w-24 items-center px-3">
                    <div
                        className={cn(
                            "flex items-center rounded-md px-2 py-1 flex-shrink-0",
                            getStatusStyle(appointment.status)
                        )}
                    >
                        <div className="text-[10px] font-medium whitespace-nowrap">
                            {appointment.status}
                        </div>
                    </div>
                </div>

                {/* Date & Time */}
                <div className="flex w-52 sm:w-44 md:w-52 min-w-32 items-center px-3">
                    <div className="text-sm text-gray-900 whitespace-nowrap">
                        {appointment.dateTime}
                    </div>
                </div>
            </div>
        );
    }
);

AllAppointmentListCard.displayName = "AllAppointmentListCard";

export { AllAppointmentListCard };
export type { Appointment, AppointmentAction }; 