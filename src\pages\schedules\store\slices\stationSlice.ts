import { useQuery } from "@tanstack/react-query";
import { GetStations } from "../../http";
import type { StationsTypes } from "../../types";
import { useOrganizationContext } from "@/features/organizations/context";

export function useGetStations(
    params: StationsTypes.GetStationsParams,
    enabled: boolean
) {
    const { organizationId } = useOrganizationContext();
    return useQuery({
        queryKey: ["stations", params],
        queryFn: () => GetStations(params, organizationId || undefined),
        enabled,
    });
}