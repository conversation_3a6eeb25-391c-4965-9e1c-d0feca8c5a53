import React from "react";
import { IntakeFieldsTable } from "@/components/common/IntakeFieldsTable";

// Example usage of the IntakeFieldsTable component
export function IntakeFieldsExample() {
	const handleAddField = () => {
		// TODO: Open add field dialog/sheet
		console.log("Add new intake field");
	};

	const handleSelectAll = (checked: boolean) => {
		console.log("Select all fields:", checked);
	};

	const handleFieldSelect = (fieldId: string, selected: boolean) => {
		console.log("Field selection changed:", fieldId, selected);
	};

	return (
		<div className="container mx-auto p-6">
			<IntakeFieldsTable
				onAddField={handleAddField}
				onSelectAll={handleSelectAll}
				onFieldSelect={handleFieldSelect}
				className="w-full"
			/>
		</div>
	);
}

export default IntakeFieldsExample;
