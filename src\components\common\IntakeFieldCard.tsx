import React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Edit, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import type { IntakeField } from "./IntakeFieldsTable";

interface IntakeFieldCardProps {
	field: IntakeField;
	isSelected: boolean;
	onSelectionChange: (selected: boolean) => void;
	onEdit?: (field: IntakeField) => void;
	onDelete?: (field: IntakeField) => void;
	onToggleRequired?: (
		fieldId: number,
		required: "yes" | "no" | "optional"
	) => void;
	onToggleActive?: (fieldId: number, active: boolean) => void;
	loadingStates?: Record<string, boolean>;
}

export function IntakeFieldCard({
	field,
	isSelected,
	onSelectionChange,
	onEdit,
	onDelete,
	onToggleRequired,
	onToggleActive,
	loadingStates = {},
}: IntakeFieldCardProps) {
	const handleToggleRequired = (checked: boolean) => {
		if (onToggleRequired) {
			// Convert boolean to the required enum type
			const requiredValue: "yes" | "no" | "optional" = checked
				? "yes"
				: "no";
			onToggleRequired(field.id, requiredValue);
		}
	};

	const handleToggleActive = (checked: boolean) => {
		if (onToggleActive) {
			onToggleActive(field.id, checked);
		}
	};

	return (
		<div className="flex h-16 items-center justify-between border-b border-gray-100 py-2 pl-4">
			{/* Selection Checkbox - handled by DataTable */}

			{/* Field Name */}
			<div className="flex flex-1 items-center px-3">
				<div className="flex items-center gap-2">
					<span className="font-medium">{field.name}</span>
					{field.type && (
						<Badge variant="secondary" className="text-xs">
							{field.type}
						</Badge>
					)}
				</div>
			</div>

			{/* Required Switch */}
			<div className="flex flex-1 items-center justify-center px-3">
				<Switch
					checked={field.field_requirement === "yes"}
					onCheckedChange={handleToggleRequired}
					disabled={loadingStates[`required-${field.id}`]}
				/>
			</div>

			{/* Active Switch */}
			<div className="flex flex-1 items-center justify-center px-3">
				<Switch
					checked={field.is_visible}
					onCheckedChange={handleToggleActive}
					disabled={loadingStates[`active-${field.id}`]}
				/>
			</div>

			{/* Actions */}
			<div className="flex flex-1 items-center justify-center px-3">
				<DropdownMenu>
					<DropdownMenuTrigger asChild>
						<Button variant="ghost" size="sm">
							<MoreHorizontal className="h-4 w-4" />
						</Button>
					</DropdownMenuTrigger>
					<DropdownMenuContent align="end">
						{onEdit && (
							<DropdownMenuItem onClick={() => onEdit(field)}>
								<Edit className="mr-2 h-4 w-4" />
								Edit
							</DropdownMenuItem>
						)}
						{onDelete && (
							<DropdownMenuItem
								onClick={() => onDelete(field)}
								className="text-red-600 hover:text-red-600"
							>
								<Trash2 className="mr-2 h-4 w-4" />
								Delete
							</DropdownMenuItem>
						)}
					</DropdownMenuContent>
				</DropdownMenu>
			</div>
		</div>
	);
}
