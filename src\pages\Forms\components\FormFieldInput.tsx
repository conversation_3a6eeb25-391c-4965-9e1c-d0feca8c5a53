import { useState } from "react";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";

import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { useFieldArray } from "react-hook-form";
import { Textarea } from "@/components/ui/textarea";
import ConditionalOptions from "./ConditionalOptions";
import { createField } from "@/pages/Forms/utils/formHelpers";
import type { FormTypes } from "@/pages/Forms/types";
import { DateValidationOptions } from "./DateValidationOptions";
import { DefaultValuesOption } from "./DefaultValuesOption";

import { Copy, Minus, Plus, Trash2 } from "lucide-react";

const customFieldTypeOptions = [
	{ value: "text", label: "Text" },
	{ value: "longtext", label: "Long Text" },
	{ value: "numeric", label: "Number" },
	{ value: "date", label: "Date" },
	{ value: "date_range", label: "Date Range" },
	{ value: "dropdown", label: "Dropdown" },
	{ value: "radio", label: "Radio" },
	{ value: "checkbox", label: "Checkbox" },
	{ value: "attachment", label: "Attachment" },
	{ value: "info_image", label: "Informational Image" },
	{ value: "info_text", label: "Informational Text" },
	{ value: "scale_1_10", label: "Scale (1-10)" },
	{ value: "satisfaction_scale", label: "Scale (Satisfied to Dissatisfied)" },
	{ value: "agree_disagree", label: "Scale (Agree to Disagree)" },
	{ value: "yes_no", label: "Yes / No" },
	{ value: "rating", label: "Rating (0-5 stars)" },
];

export const FormFieldInput = ({
	nestIndex,
	field,
	control,
	watch,
	setValue,
	getValues,
	clearErrors,
	form,
}: {
	nestIndex: number;
	field: any;
	control: any;
	watch: any;
	setValue: any;
	getValues: any;
	clearErrors: any;
	form: any;
}) => {
	const [showDescription, setShowDescription] = useState(false);
	const fieldValue = field?.value || {};
	const fieldType = fieldValue.type || "text";

	const handleTypeChange = (value: string) => {
		const currentValues = getValues(
			`sections.${nestIndex}.fields.${field.name}`
		);
		const preserveValues = {
			title: currentValues?.title || "",
			description: currentValues?.description || "",
		};

		const newField = createField(
			value as FormTypes.FieldType,
			preserveValues
		);

		setValue(`sections.${nestIndex}.fields.${field.name}`, newField, {
			shouldValidate: false,
			shouldDirty: true,
			shouldTouch: false,
		});

		const fieldPath = `sections.${nestIndex}.fields.${field.name}`;
		clearErrors(fieldPath);
	};

	const { fields: sectionFields, update: updateSection } = useFieldArray({
		control,
		name: "sections",
	});

	const { remove } = useFieldArray({
		control,
		name: `sections.${nestIndex}.fields`,
	});

	const handleDuplicateField = (sectionIndex: number, fieldIndex: number) => {
		const section: any = sectionFields[sectionIndex];

		if (section && section.fields) {
			const fieldToDuplicate = section.fields[fieldIndex];
			if (fieldToDuplicate) {
				const currentValues = getValues(
					`sections.${sectionIndex}.fields.${fieldIndex}`
				);

				const duplicatedField = {
					...fieldToDuplicate,
					...currentValues,
					id: crypto.randomUUID(),
					order: fieldIndex + 1,
				};

				const newFields = [...section.fields];
				newFields.splice(fieldIndex + 1, 0, duplicatedField);

				const updatedSection = {
					...section,
					fields: newFields,
				};

				updateSection(sectionIndex, updatedSection);
			}
		}
	};

	const handleDelete = () => {
		remove(field.name);
	};

	const formtype = getValues("type");

	return (
		<div className="space-y-4">
			<div className="flex flex-col gap-6">
				<div className="flex-1">
					<FormField
						control={control}
						name={`sections.${nestIndex}.fields.${field.name}.title`}
						render={({ field: titleField }) => (
							<FormItem>
								<FormLabel className="text-xs font-medium">
									{fieldType === "info_image"
										? "Enter Informational Text"
										: "Field Title"}{" "}
									<span className="text-red-500">*</span>
								</FormLabel>
								<FormControl>
									<Input
										{...titleField}
										placeholder="Field Title"
										className="text-xs placeholder:text-xs"
										disabled={
											formtype === "intake" &&
											nestIndex == 0 &&
											field.name < 4
										}
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>
				{!showDescription && (
					<button
						type="button"
						className="-mt-2 ml-2.5 flex cursor-pointer items-center gap-3 text-xs font-normal text-[#060D25]"
						onClick={() => setShowDescription(!showDescription)}
					>
						<svg
							width="14"
							height="14"
							viewBox="0 0 14 14"
							fill="none"
							xmlns="http://www.w3.org/2000/svg"
						>
							<path
								d="M4.6665 7.00033H9.33317M6.99984 4.66699V9.33366M12.8332 7.00033C12.8332 10.222 10.2215 12.8337 6.99984 12.8337C3.77818 12.8337 1.1665 10.222 1.1665 7.00033C1.1665 3.77866 3.77818 1.16699 6.99984 1.16699C10.2215 1.16699 12.8332 3.77866 12.8332 7.00033Z"
								stroke="#005893"
								strokeWidth="1.5"
								strokeLinecap="round"
								strokeLinejoin="round"
							/>
						</svg>
						<span className="leading-none">Add Description</span>
					</button>
				)}
				{showDescription && (
					<div>
						<FormField
							control={control}
							name={`sections.${nestIndex}.fields.${field.name}.description`}
							render={({ field: descriptionField }) => (
								<FormItem>
									<FormLabel className="text-xs">
										Description
									</FormLabel>
									<FormControl>
										<Textarea
											{...descriptionField}
											placeholder="Add a description for this field"
											className="mt-2 text-xs placeholder:text-xs"
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<button
							type="button"
							className="!mt-2 ml-2.5 flex items-center gap-2 text-xs leading-none font-normal text-[#323539]"
							onClick={() => setShowDescription(false)}
						>
							<Minus
								color="#fff"
								className="rounded-full bg-red-600"
								size={12}
							/>
							Hide Description
						</button>
					</div>
				)}

				{(formtype !== "intake" ||
					(formtype === "intake" && nestIndex > 0) ||
					(formtype == "intake" &&
						nestIndex == 0 &&
						field.name > 3)) && (
					<div className="w-full border-t pt-6">
						<FormField
							control={control}
							name={`sections.${nestIndex}.fields.${field.name}.type`}
							render={({ field: typeField }) => (
								<FormItem>
									<FormLabel className="text-xs font-medium">
										Field Type{" "}
										<span className="text-red-500">*</span>
									</FormLabel>
									<Select
										onValueChange={(value) => {
											handleTypeChange(value as string);
											typeField.onChange(value);
										}}
										value={typeField.value}
									>
										<SelectTrigger className="w-full text-xs placeholder:text-xs">
											<SelectValue placeholder="Select type" />
										</SelectTrigger>
										<SelectContent>
											{customFieldTypeOptions.map(
												(option) => (
													<SelectItem
														key={option.value}
														value={option.value}
													>
														{option.label}
													</SelectItem>
												)
											)}
										</SelectContent>
									</Select>
									<FormMessage />
								</FormItem>
							)}
						/>
					</div>
				)}
			</div>

			{(formtype !== "intake" ||
				(formtype === "intake" && nestIndex > 0) ||
				(formtype == "intake" && nestIndex == 0 && field.name > 3)) &&
				(fieldType === "checkbox" ||
					fieldType === "radio" ||
					fieldType === "dropdown" ||
					fieldType === "attachment" ||
					fieldType === "info_image" ||
					fieldType === "info_text") && (
					<ConditionalOptions
						nestIndex={nestIndex}
						field={field}
						control={control}
						watch={watch}
						setValue={setValue}
					/>
				)}

			{(formtype !== "intake" ||
				(formtype === "intake" && nestIndex > 0) ||
				(formtype == "intake" && nestIndex == 0 && field.name > 3)) && (
				<>
					<DateValidationOptions
						sectionIndex={nestIndex}
						field={field}
						control={control}
						watch={watch}
						form={form}
					/>

					<DefaultValuesOption
						sectionIndex={nestIndex}
						field={field}
						control={control}
						watch={watch}
						form={form}
					/>
				</>
			)}

			<div className="flex items-center justify-end space-x-3 divide-x-2">
				<div className="flex items-center space-x-3">
					<Button
						type="button"
						variant="outline"
						onClick={handleDelete}
						className="h-auto w-auto cursor-pointer !p-1.5"
						disabled={
							formtype === "intake" &&
							nestIndex == 0 &&
							field.name < 4
						}
					>
						<Trash2 className="h-2.5 w-2.5 text-black" />
					</Button>
					<Button
						type="button"
						variant="outline"
						className="mr-3 h-auto w-max cursor-pointer !p-1.5"
						onClick={() =>
							handleDuplicateField(nestIndex, field.name)
						}
						disabled={
							formtype === "intake" &&
							nestIndex == 0 &&
							field.name < 4
						}
					>
						<Copy className="h-2.5 w-2.5 text-black" />
					</Button>
				</div>
				<div className="flex items-center space-x-2 pl-3">
					<Switch
						checked={field.value?.required}
						onCheckedChange={(checked) =>
							field.onChange({
								...field.value,
								required: checked,
							})
						}
						disabled={
							field.value?.type === "info_image" ||
							(formtype === "intake" &&
								nestIndex == 0 &&
								field.name < 4)
						}
						className="data-[state=checked]:bg-[#28C466]"
					/>
					<span className="text-sm font-medium">Required</span>
				</div>
			</div>
		</div>
	);
};

// formtype !== "intake" || (formtype === "intake" && nestIndex > 0);
