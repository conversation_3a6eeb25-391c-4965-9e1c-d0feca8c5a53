import { FormResponseTypes } from "../types";

export const transformApiResponseToFormResponses = (
	responseData: FormResponseTypes.FormResponse
): FormResponseTypes.FormResponse[] => {
	// Transform field responses to FormFieldResponse format
	const fieldResponses: FormResponseTypes.FormFieldResponse[] =
		responseData.field_responses.map((response) => ({
			id: `${responseData.uuid}_${response.field_id}`, // Generate unique ID
			form_response_id: responseData.uuid,
			field_id: response.field_id,
			value: response.value,
			value_type: response.value_type,
			created_at: responseData.created_at,
			updated_at: responseData.updated_at,
		}));

	// Return a single FormResponse containing all field responses
	return [
		{
			uuid: responseData.uuid,
			form_id: responseData.form_id,
			status: responseData.status,
			block_reason: responseData.block_reason || undefined,
			session_id: responseData.session_id,
			submitted_at: responseData.submitted_at,
			created_at: responseData.created_at,
			updated_at: responseData.updated_at,
			field_responses: fieldResponses,
		},
	];
};

// Reverse transformer - converts FormResponse back to update payload
export const transformFormResponsesToUpdatePayload = (
	responses: FormResponseTypes.FormResponse[],
	userId?: number
): FormResponseTypes.UpdateFormResponsePayload => {
	const fieldResponses = responses.flatMap(
		(response) => response.field_responses || []
	);

	return {
		responses: fieldResponses.map((fieldResponse) => ({
			field_id: fieldResponse.field_id,
			value: fieldResponse.value,
		})),
		...(userId && { user_id: userId }),
	};
};
