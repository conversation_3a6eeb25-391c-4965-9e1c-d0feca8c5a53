import React, { useCallback, useState, useEffect, useMemo } from 'react';
import { ChevronLeft, Search, SlidersHorizontal, ChevronRight, Users, MapPin, QrCode, UserCheck, Loader2 } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useAllStations } from '@/features/locations/hooks/useStations';
import { useOrganizationContext } from '@/features/organizations/context';
import type { StationData } from '@/features/locations/api/stationsApi';
import { ProviderFilterSheet } from './ProviderFilterSheet';

interface ProviderSelectionProps {
  onBack: () => void;
  onSelectProvider: (provider: StationData) => void;
}

interface ProviderFilters {
  search?: string;
  locationId?: number;
  hasServiceProviders?: boolean;
  sortBy?: 'name' | 'locations' | 'providers';
  sortOrder?: 'asc' | 'desc';
}

export const ProviderSelection: React.FC<ProviderSelectionProps> = ({ onBack, onSelectProvider }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedProviders, setSelectedProviders] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [showFilterSheet, setShowFilterSheet] = useState(false);
  const [appliedFilters, setAppliedFilters] = useState<ProviderFilters>({});

  const { organizationId } = useOrganizationContext();

  const {
    data: stationsResponse,
    isLoading,
    error,
    refetch,
  } = useAllStations({
    organizationId: organizationId!,
    enabled: !!organizationId,
  });

  // Get stations data from the response
  const allStations = stationsResponse?.data || [];

  // Client-side filtering and search
  const filteredStations = useMemo(() => {
    let filtered = [...allStations];

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(station =>
        station.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        station.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        station.locations.some(location =>
          location.name.toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
    }

    // Apply additional filters
    if (appliedFilters.locationId) {
      filtered = filtered.filter(station =>
        station.locations.some(location => location.id === appliedFilters.locationId)
      );
    }

    if (appliedFilters.hasServiceProviders !== undefined) {
      filtered = filtered.filter(station => {
        const hasProviders = station.service_providers.length > 0;
        return appliedFilters.hasServiceProviders ? hasProviders : !hasProviders;
      });
    }

    // Apply sorting
    if (appliedFilters.sortBy) {
      filtered.sort((a, b) => {
        let aValue: string | number;
        let bValue: string | number;

        switch (appliedFilters.sortBy) {
          case 'name':
            aValue = a.name.toLowerCase();
            bValue = b.name.toLowerCase();
            break;
          case 'locations':
            aValue = a.locations.length;
            bValue = b.locations.length;
            break;
          case 'providers':
            aValue = a.service_providers.length;
            bValue = b.service_providers.length;
            break;
          default:
            return 0;
        }

        if (appliedFilters.sortOrder === 'desc') {
          return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
        } else {
          return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
        }
      });
    }

    return filtered;
  }, [allStations, searchTerm, appliedFilters]);

  // Pagination
  const itemsPerPage = 7;
  const totalPages = Math.ceil(filteredStations.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedStations = filteredStations.slice(startIndex, endIndex);

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      setCurrentPage(1); // Reset to first page when searching
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  const handleProviderToggle = (providerId: string) => {
    setSelectedProviders(prev =>
      prev.includes(providerId)
        ? prev.filter(id => id !== providerId)
        : [...prev, providerId]
    );
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked && paginatedStations?.length) {
      setSelectedProviders(paginatedStations.map((station) => station.id.toString()));
    } else {
      setSelectedProviders([]);
    }
  };

  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  const handlePreviousPage = useCallback(() => {
    if (currentPage > 1) {
      handlePageChange(currentPage - 1);
    }
  }, [currentPage, handlePageChange]);

  const handleNextPage = useCallback(() => {
    if (currentPage < totalPages) {
      handlePageChange(currentPage + 1);
    }
  }, [currentPage, totalPages, handlePageChange]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleApplyFilters = (filters: ProviderFilters) => {
    setAppliedFilters(filters);
    setCurrentPage(1); // Reset to first page when filters change
    setShowFilterSheet(false);
  };

  const handleClearFilters = () => {
    setAppliedFilters({});
    setSearchTerm('');
    setCurrentPage(1);
  };

  const getProviderInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const hasActiveFilters = Object.keys(appliedFilters).length > 0 || searchTerm;

  return (
    <div className="min-h-screen">
      {/* Header */}
      <div className="bg-white py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm" onClick={onBack} className="p-2">
              <ChevronLeft className="h-5 w-5" />
            </Button>
            <div>
              <h1 className="text-2xl font-semibold text-main-1">Select a Provider</h1>
              <p className="text-[#6D748D] mt-1">Select a Provider (Station) that you would like to set preferences for below.</p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <Button variant="outline" size="sm" onClick={() => refetch()}>
              {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Search className="h-4 w-4" />}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilterSheet(true)}
              className={hasActiveFilters ? "bg-blue-50 border-blue-200" : ""}
            >
              <SlidersHorizontal className="h-4 w-4" />
            </Button>
            {hasActiveFilters && (
              <Button variant="outline" size="sm" onClick={handleClearFilters}>
                Clear Filters
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="">
        {/* Search bar */}
        <div className="mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search providers..."
              value={searchTerm}
              onChange={handleSearchChange}
              className="pl-10"
            />
          </div>
        </div>

        {/* Providers List */}
        <div className='rounded-lg border border-[#E4E4E7] p-0.5'>
          <Table>
            <TableHeader className='hover:bg-transparent'>
              <TableRow className="bg-gray-50 hover:bg-transparent h-14 text-[#71717A]">
                <TableHead className="text-[#71717A]">
                  <Checkbox
                    checked={paginatedStations.length > 0 && selectedProviders.length === paginatedStations.length}
                    onCheckedChange={handleSelectAll}
                  />
                </TableHead>
                <TableHead className="text-[#71717A]">Provider</TableHead>
                <TableHead className="text-[#71717A]">Locations</TableHead>
                <TableHead className="text-[#71717A]">Service Providers</TableHead>
                <TableHead className="text-[#71717A] text-center">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin mx-auto text-blue-500" />
                    <p className="mt-2 text-gray-500">Loading providers...</p>
                  </TableCell>
                </TableRow>
              ) : error ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-8 text-red-500">
                    Error loading providers. Please try again.
                  </TableCell>
                </TableRow>
              ) : paginatedStations.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-8 text-gray-500">
                    No providers found.
                  </TableCell>
                </TableRow>
              ) : (
                paginatedStations.map((station) => (
                  <TableRow
                    key={station.id}
                    className={`hover:bg-gray-50 h-16 cursor-pointer ${selectedProviders.includes(station.id.toString()) ? 'bg-blue-50' : ''
                      }`}
                    onClick={() => onSelectProvider(station)}
                  >
                    <TableCell>
                      <Checkbox
                        checked={selectedProviders.includes(station.id.toString())}
                        onCheckedChange={() => handleProviderToggle(station.id.toString())}
                        onClick={(e) => e.stopPropagation()}
                      />
                    </TableCell>
                    <TableCell className="font-medium">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center text-sm font-medium text-gray-600 overflow-hidden">
                          {station.image ? (
                            <img
                              src={station.image}
                              alt={station.name}
                              className="w-full h-full object-cover rounded-full"
                              onError={(e) => {
                                // Fallback to initials if image fails to load
                                const target = e.target as HTMLImageElement;
                                target.style.display = 'none';
                                target.nextElementSibling!.style.display = 'flex';
                              }}
                            />
                          ) : null}
                          <div
                            className={`w-full h-full flex items-center justify-center text-sm font-medium text-gray-600 ${station.image ? 'hidden' : 'flex'}`}
                          >
                            {getProviderInitials(station.name)}
                          </div>
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-900">{station.name}</h3>
                          <p className="text-sm text-gray-500">
                            {station.description || 'No description available'}
                          </p>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2 text-sm text-gray-600">
                        <MapPin className="h-4 w-4" />
                        <span>Locations</span>
                        <span className="font-medium">{station.locations?.length || 0}</span>
                      </div>
                      {station.locations.length > 0 && (
                        <div className="text-xs text-gray-500 mt-1">
                          {station.locations.slice(0, 2).map(loc => loc.name).join(', ')}
                          {station.locations.length > 2 && ` +${station.locations.length - 2} more`}
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2 text-sm text-gray-600">
                        <UserCheck className="h-4 w-4" />
                        <span>Providers</span>
                        <span className="font-medium">{station.service_providers?.length || 0}</span>
                      </div>
                      {station.service_providers.length > 0 && (
                        <div className="text-xs text-gray-500 mt-1">
                          {station.service_providers.slice(0, 2).map(sp => `${sp.first_name} ${sp.last_name || ''}`.trim()).join(', ')}
                          {station.service_providers.length > 2 && ` +${station.service_providers.length - 2} more`}
                        </div>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end space-x-2">
                        <Button variant="outline" className="px-2 py-1 text-xs font-medium">
                          <QrCode className="h-4 w-4 text-gray-400" />
                        </Button>
                        <Button variant="outline" className="px-2 py-1 text-xs font-medium">
                          <ChevronRight className="h-4 w-4 text-gray-400" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        {paginatedStations.length > 0 && totalPages > 1 && (
          <div className="flex justify-end pt-4">
            <div>
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={handlePreviousPage}
                      className={
                        currentPage === 1
                          ? "pointer-events-none opacity-50"
                          : "cursor-pointer"
                      }
                    />
                  </PaginationItem>

                  {/* Show current page and a few around it */}
                  {[currentPage - 1, currentPage, currentPage + 1]
                    .filter(page => page > 0 && page <= totalPages)
                    .map((page) => (
                      <PaginationItem key={page}>
                        <PaginationLink
                          onClick={() => handlePageChange(page)}
                          isActive={currentPage === page}
                          className="cursor-pointer"
                        >
                          {page}
                        </PaginationLink>
                      </PaginationItem>
                    ))}

                  <PaginationItem>
                    <PaginationNext
                      onClick={handleNextPage}
                      className={
                        currentPage === totalPages
                          ? "pointer-events-none opacity-50"
                          : "cursor-pointer"
                      }
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          </div>
        )}
      </div>

      {/* Filter Sheet */}
      <ProviderFilterSheet
        open={showFilterSheet}
        onOpenChange={setShowFilterSheet}
        onApplyFilters={handleApplyFilters}
        currentFilters={appliedFilters}
        availableLocations={allStations.flatMap(s => s.locations).filter((loc, index, arr) =>
          arr.findIndex(l => l.id === loc.id) === index
        )}
      />
    </div>
  );
};