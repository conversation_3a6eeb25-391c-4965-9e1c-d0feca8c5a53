import { useQuery } from "@tanstack/react-query";
import { GetServices } from "../../http";
import { useOrganizationContext } from "@/features/organizations/context";
import type { ServicesTypes } from "../../types";

export function useGetServices(
    params: ServicesTypes.GetServicesParams,
) {
    const { organizationId } = useOrganizationContext();

    return useQuery({
        queryKey: ["services", params],
        queryFn: () => GetServices(params, organizationId || undefined),
    });
}
