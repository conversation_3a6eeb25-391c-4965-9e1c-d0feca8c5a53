import { useMutation, useQuery } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import type { UseFormSetError } from 'react-hook-form';
import { loginUser, googleLogin, microsoftLogin, verify2FA, logoutUser, APIVersion1SendVerifyEmail, APIVersion1VerifySSODomain, APIVersion1VerifyTenant, getProfile } from '../../lib/api/auth';
import type {
  LoginRequest,
  LoginResponse,
  TwoFAVerifyRequest,
  TwoFAVerifyResponse,
  LogoutResponse,
  User
} from '@/types/api/auth';
import { useAuthStore } from '../authStore';
import { useUIStore } from '../uiStore';
import { AxiosError, type AxiosResponse } from 'axios';
import { ROUTES } from '@/lib/utils/constants';
import { handleAuthError, handle2FAError, extractValidationErrors, logError } from '@/lib/utils/errorHandling';
import { useQueryClient } from '@tanstack/react-query';

const extractUserFromLogin = (data: any) => {

  const {
    id,
    name,
    email,
    intercom_user_hash,
    is_email_verified,
    two_factor_enable,
    business_count
  } = data;

  return {
    id,
    name,
    email,
    intercom_user_hash,
    is_email_verified,
    two_factor_enable,
    business_count,
    role: typeof data.role === 'string' ? data.role : '',
    locationIds: Array.isArray(data.locationIds) ? data.locationIds : [],
    permissions: Array.isArray(data.permissions) ? data.permissions : [],
  };
};

// Helper function to fetch profile after successful login
const fetchProfileAfterLogin = async (queryClient: any) => {
  try {
    const profileResponse = await getProfile();
    queryClient.setQueryData(['profile'], profileResponse);
    return profileResponse.data;
  } catch (error) {
    console.error('Failed to fetch profile after login:', error);
    return null;
  }
};

export const useLoginUser = (setError: UseFormSetError<LoginRequest>) => {
  const navigate = useNavigate();
  const { setAuth } = useAuthStore();
  const { addToast } = useUIStore();
  const queryClient = useQueryClient();

  return useMutation<LoginResponse, AxiosError, LoginRequest>({
    mutationFn: loginUser,
    onSuccess: async (data) => {
      if (data.data.two_factor_enable && !data.data.two_factor_skip) {
        // Handle 2FA flow - store temporary token
        localStorage.setItem('mfa_token', data.data.token);
        navigate(ROUTES.AUTH.MFA);
      } else {
        // Handle successful login
        setAuth(
          extractUserFromLogin(data.data),
          typeof data.data.token === 'string' ? data.data.token : '',
          typeof data.data.remember_token === 'string' ? data.data.remember_token : ''
        );

        // Fetch profile information after successful login
        await fetchProfileAfterLogin(queryClient);

        addToast({
          type: 'success',
          title: 'Login Successful',
          message: 'Welcome back!'
        });
        navigate(ROUTES.HOME);
      }
    },
    onError: (error) => {
      const validationErrors = extractValidationErrors(error);

      if (Object.keys(validationErrors).length > 0) {
        // Handle validation errors
        Object.entries(validationErrors).forEach(([field, message]) => {
          setError(field as keyof LoginRequest, {
            message,
          });
        });
      } else {
        const errorMessage = handleAuthError(error);
        logError(error, "Login Error");
        addToast({
          type: 'error',
          title: 'Login Failed',
          message: errorMessage
        });
      }
    },
  });
};

// Google OAuth login mutation
export const useGoogleLogin = () => {
  const navigate = useNavigate();
  const { setAuth } = useAuthStore();
  const { addToast } = useUIStore();
  const queryClient = useQueryClient();

  return useMutation<any, AxiosError, { token: string }>({
    mutationFn: googleLogin,
    onSuccess: async (data) => {
      if (data.data?.two_factor_enable && !data.data?.two_factor_skip) {
        localStorage.setItem('mfa_token', data.data.token);
        navigate(ROUTES.AUTH.MFA);
      } else {
        setAuth(
          extractUserFromLogin(data.data),
          typeof data.data.token === 'string' ? data.data.token : '',
          typeof data.data.remember_token === 'string' ? data.data.remember_token : ''
        );

        // Fetch profile information after successful login
        await fetchProfileAfterLogin(queryClient);

        addToast({
          type: 'success',
          title: 'Login Successful',
          message: 'Welcome back!'
        });
        navigate(ROUTES.HOME);
      }
    },
    onError: (error) => {
      const errorMessage = handleAuthError(error);
      logError(error, "Google Login Error");
      addToast({
        type: 'error',
        title: 'Google Login Failed',
        message: errorMessage
      });
    },
  });
};

// Microsoft OAuth login mutation
export const useMicrosoftLogin = () => {
  const navigate = useNavigate();
  const { setAuth } = useAuthStore();
  const { addToast } = useUIStore();
  const queryClient = useQueryClient();

  return useMutation<any, AxiosError, { token: string }>({
    mutationFn: microsoftLogin,
    onSuccess: async (data) => {
      if (data.data?.two_factor_enable && !data.data?.two_factor_skip) {
        localStorage.setItem('mfa_token', data.data.token);
        navigate(ROUTES.AUTH.MFA);
      } else {
        setAuth(
          extractUserFromLogin(data.data),
          typeof data.data.token === 'string' ? data.data.token : '',
          typeof data.data.remember_token === 'string' ? data.data.remember_token : ''
        );

        // Fetch profile information after successful login
        await fetchProfileAfterLogin(queryClient);

        addToast({
          type: 'success',
          title: 'Login Successful',
          message: 'Welcome back!'
        });
        navigate(ROUTES.HOME);
      }
    },
    onError: (error) => {
      const errorMessage = handleAuthError(error);
      logError(error, "Microsoft Login Error");
      addToast({
        type: 'error',
        title: 'Microsoft Login Failed',
        message: errorMessage
      });
    },
  });
};

// 2FA verification mutation
export const useVerify2FA = (setError: UseFormSetError<TwoFAVerifyRequest>) => {
  const navigate = useNavigate();
  const { setAuth } = useAuthStore();
  const { addToast } = useUIStore();
  const queryClient = useQueryClient();

  return useMutation<TwoFAVerifyResponse, AxiosError, TwoFAVerifyRequest>({
    mutationFn: verify2FA,
    onSuccess: async (data) => {
      // Set auth with the verified user data
      setAuth(
        extractUserFromLogin(data.user as any),
        localStorage.getItem('mfa_token') || '',
        ''
      );

      // Clear the temporary MFA token
      localStorage.removeItem('mfa_token');

      // Fetch profile information after successful 2FA verification
      await fetchProfileAfterLogin(queryClient);

      addToast({
        type: 'success',
        title: '2FA Verification Successful',
        message: 'Welcome back!'
      });

      navigate(ROUTES.HOME);
    },
    onError: (error) => {
      const validationErrors = extractValidationErrors(error);

      if (Object.keys(validationErrors).length > 0) {
        Object.entries(validationErrors).forEach(([field, message]) => {
          setError(field as keyof TwoFAVerifyRequest, {
            message,
          });
        });
      } else {
        const errorMessage = handle2FAError(error);
        logError(error, "2FA Verification Error");
        addToast({
          type: 'error',
          title: '2FA Verification Failed',
          message: errorMessage
        });
      }
    },
  });
};

// Logout mutation
export const useLogout = () => {
  const navigate = useNavigate();
  const { logout } = useAuthStore();
  const { addToast } = useUIStore();

  return useMutation<LogoutResponse, AxiosError>({
    mutationFn: logoutUser,
    onSuccess: () => {
      logout();
      localStorage.removeItem('mfa_token');
      addToast({
        type: 'success',
        title: 'Logged Out',
        message: 'You have been successfully logged out'
      });
      navigate(ROUTES.AUTH.SIGNIN);
    },
    onError: () => {
      // Even if logout API fails, clear local state
      logout();
      localStorage.removeItem('mfa_token');
      navigate(ROUTES.AUTH.SIGNIN);
    },
  });
};

export const ResendVerificationEmailSlice = () => {
  return useMutation<
    AxiosResponse<Record<string, string>>,
    Error,
    { email: string }
  >({
    mutationFn: APIVersion1SendVerifyEmail,
  });
};


export const useVerifySSOTenant = (domain: string | null) => {
  return useQuery<AxiosResponse<User>, AxiosError>({
    enabled: !!domain,
    queryKey: ["verify-sso-tenant", domain],
    queryFn: () => {
      if (!domain) {
        throw new Error("Domain is required for SSO verification");
      }
      return APIVersion1VerifyTenant(domain);
    },
    retry: 3,
  });
};

export const useVerifySSO = (domain: string | null) => {
  return useQuery<AxiosResponse<User>, AxiosError>({
    enabled: false,
    queryKey: ["verify-sso", domain],
    queryFn: () => {
      if (!domain) {
        throw new Error("Domain is required for SSO verification");
      }
      return APIVersion1VerifySSODomain(domain);
    },
    retry: 3,
  });
};
