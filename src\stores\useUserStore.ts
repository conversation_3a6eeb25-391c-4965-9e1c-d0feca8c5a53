import type { OperatingHour } from "@/types/onboarding";
import type { AuthUserData } from "@/types/signin";
import type { AuthTwoEnabledFactorResponse } from "@/types/signup";
import { create } from "zustand";
import { persist } from "zustand/middleware";

interface AdminStore {
	user: AuthUserData | null;
	selectedOrganisation: any;
	onboardingState: number;
	onboardingLocationInfo: onboardingLocationInfoType | null;
	rememberAuth: {
		rememberMe?: boolean;
		rememberToken?: string;
	} | null;
	mfaUser: AuthTwoEnabledFactorResponse | null;
	setOnboardingState: (newState: number) => void;
	setOnboardingLocationInfo: (
		onboardingState: onboardingLocationInfoType | null
	) => void;
	setUser: (user: AuthUserData | null) => void;
	setRememberAuth: (
		rememberAuth: {
			rememberMe?: boolean;
			rememberToken?: string;
		} | null
	) => void;
	setMfaUser: (mfaUser: AuthTwoEnabledFactorResponse | null) => void;
	resetMfaUser: () => void;
	reset: () => void;
}

interface onboardingLocationInfoType {
	id?: number;
	approximate_waiting_time: number | string;
	schedule_block_in_min: number | string;
	time_zone: string;
	time_slots: OperatingHour[];
	
}

const initialState = {
	user: null,
	mfaUser: null,
	onboardingState: 1,
	onboardingLocationInfo: null,
	selectedOrganisation: "",
	rememberAuth: {
		rememberMe: false,
	},
};

const useUserStore = create<AdminStore, [["zustand/persist", AdminStore]]>(
	persist(
		(set: any) => ({
			...initialState,
			setUser: (user: any) => {
				if (user)
					set((state: any) => {
						// console.log({ user: { ...state.user, ...user } });
						return { user: { ...state.user, ...user } };
					});
			},
			setOnboardingLocationInfo: (onboardingLocationInfo: any) =>
				set(() => ({ onboardingLocationInfo })),
			setOnboardingState: (onboardingState: any) => {
				set(() => ({
					onboardingState,
				}));
			},

			setRememberAuth: (rememberAuth: any) => {
				set((state: any) => ({
					rememberAuth: { ...state.rememberAuth, ...rememberAuth },
				}));
			},
			setMfaUser: (mfaUser: any) => {
				if (mfaUser)
					set((state: any) => {
						return { mfaUser: { ...state.mfaUser, ...mfaUser } };
					});
			},
			resetMfaUser: () => {
				set(() => {
					return { mfaUser: null };
				});
			},
			reset: () => set(initialState),
		}),
		{
			name: "user-storage",
			// storage: () => localStorage,
		}
	)
);

export default useUserStore;
