
import React, { useEffect, useRef, useState } from "react";
	import { <PERSON>, <PERSON><PERSON><PERSON>, Toolt<PERSON> } from "recharts";
	// import { AnalyticMetric } from "./MetricCard";
import { cn } from "@/lib/utils";
import type { AnalyticMetric } from "./MetricCard";

const MultiProgressRing: React.FC<{
	ratios: AnalyticMetric["ratios"];
	legend: AnalyticMetric["legend"];
	size?: number;
	innerRadius?: number;
}> = ({ legend, ratios, size = 75, innerRadius = size / 2 - 12 }) => {
	// console.log(ratios);
	const [animationData, setAnimationData] = useState<
		Array<{ value: number; name: string; fill: string }>
	>([]);

	useEffect(() => {
		if (!ratios?.length) return;
		const timer = setTimeout(() => {
			setAnimationData(
				ratios.map((ratio: any, index) => ({
					name:
						legend && legend[index]
							? legend[index].label
							: `Segment ${index + 1}`,
					value: ratio.value,
					fill: ratio.color,
				}))
			);
		}, 100);
		return () => clearTimeout(timer);
	}, [ratios]);

	if (!ratios?.length) return null;
	return (
		<div className="relative flex items-center justify-center">
			<PieChart width={size} height={size}>
				<Pie
					data={animationData}
					dataKey="value"
					nameKey="name"
					innerRadius={innerRadius}
					outerRadius={size / 2}
					strokeWidth={0}
					startAngle={0}
					// endAngle={-270}
					animationDuration={1000}
					cornerRadius={20}
				/>
				<Tooltip
					content={(props) => (
						<MerticCustomTooltip
							active={props.active}
							payload={props.payload}
						/>
					)}
				/>
			</PieChart>
		</div>
	);
};

export default MultiProgressRing;

export const MerticCustomTooltip: React.FC<{
	active?: boolean;
	payload: any;
	className?: string;
}> = ({ active, payload, className }) => {
	const ref = useRef(null);

	if (!active || !payload || !payload.length) {
		return null;
	}
	console.log(payload);
	return (
		<div
			ref={ref}
			className={cn(
				"grid w-[9rem] min-w-fit items-start gap-1.5 rounded-lg border border-border/50 bg-background px-2.5 py-1.5 text-xs shadow-xl",
				className
			)}
		>
			<div className="grid gap-1.5">
				{payload.map((item, index) => {
					const indicatorColor = item.payload.fill || item.color;

					return (
						<div
							key={index}
							className="flex w-full flex-wrap items-center gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground"
						>
							<div
								className="h-2.5 w-2.5 shrink-0 rounded-[2px] border-[--color-border] bg-[--color-bg]"
								style={
									{
										"--color-bg": indicatorColor,
										"--color-border": indicatorColor,
									} as React.CSSProperties
								}
							/>
							<div className="flex flex-1 items-center justify-between leading-none">
								<span className="text-muted-foreground">
									{item.name}
								</span>
								<span className="ml-[35px] font-mono font-medium tabular-nums text-foreground">
									{item.value.toLocaleString()}
								</span>
							</div>
						</div>
					);
				})}
			</div>
		</div>
	);
};
