import React, { useState, useEffect } from 'react';
import { X, Filter } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
    Sheet,
    SheetContent,
    SheetHeader,
    SheetTitle,
    SheetDescription,
    SheetFooter,
} from '@/components/ui/sheet';

interface ProviderFilters {
    search?: string;
    locationId?: number;
    hasServiceProviders?: boolean;
    sortBy?: 'name' | 'locations' | 'providers';
    sortOrder?: 'asc' | 'desc';
}

interface Location {
    id: number;
    name: string;
}

interface ProviderFilterSheetProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    onApplyFilters: (filters: ProviderFilters) => void;
    currentFilters: ProviderFilters;
    availableLocations: Location[];
}

export function ProviderFilterSheet({
    open,
    onOpenChange,
    onApplyFilters,
    currentFilters,
    availableLocations,
}: ProviderFilterSheetProps) {
    const [localFilters, setLocalFilters] = useState<ProviderFilters>({});

    // Initialize local filters when sheet opens
    useEffect(() => {
        if (open) {
            setLocalFilters(currentFilters);
        }
    }, [open, currentFilters]);

    const handleLocationChange = (locationId: string) => {
        setLocalFilters(prev => ({
            ...prev,
            locationId: locationId === 'all' ? undefined : parseInt(locationId),
        }));
    };

    const handleServiceProviderFilterChange = (value: string) => {
        setLocalFilters(prev => ({
            ...prev,
            hasServiceProviders: value === 'all' ? undefined : value === 'with-providers',
        }));
    };

    const handleSortChange = (value: string) => {
        const [sortBy, sortOrder] = value.split('-') as [string, 'asc' | 'desc'];
        setLocalFilters(prev => ({
            ...prev,
            sortBy: sortBy as ProviderFilters['sortBy'],
            sortOrder,
        }));
    };

    const handleApply = () => {
        onApplyFilters(localFilters);
    };

    const handleReset = () => {
        setLocalFilters({});
    };

    const getSortValue = () => {
        if (localFilters.sortBy && localFilters.sortOrder) {
            return `${localFilters.sortBy}-${localFilters.sortOrder}`;
        }
        return 'name-asc';
    };

    const getLocationValue = () => {
        if (localFilters.locationId) {
            return localFilters.locationId.toString();
        }
        return 'all';
    };

    const getServiceProviderValue = () => {
        if (localFilters.hasServiceProviders === undefined) {
            return 'all';
        }
        return localFilters.hasServiceProviders ? 'with-providers' : 'without-providers';
    };

    return (
        <Sheet open={open} onOpenChange={onOpenChange}>
            <SheetContent className="w-[400px] sm:w-[500px]">
                <SheetHeader>
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                            <Filter className="h-5 w-5" />
                            <SheetTitle>Filter Providers</SheetTitle>
                        </div>
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onOpenChange(false)}
                            className="h-6 w-6 p-0"
                        >
                            <X className="h-4 w-4" />
                        </Button>
                    </div>
                    <SheetDescription>
                        Filter providers by location, service provider availability, and sort preferences.
                    </SheetDescription>
                </SheetHeader>

                <div className="py-6 space-y-6">
                    {/* Location Filter */}
                    <div className="space-y-2">
                        <Label className="text-sm font-medium">Filter by Location</Label>
                        <Select value={getLocationValue()} onValueChange={handleLocationChange}>
                            <SelectTrigger>
                                <SelectValue placeholder="Select location" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">All Locations</SelectItem>
                                {availableLocations.map((location) => (
                                    <SelectItem key={location.id} value={location.id.toString()}>
                                        {location.name}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>

                    {/* Service Provider Filter */}
                    <div className="space-y-2">
                        <Label className="text-sm font-medium">Service Provider Availability</Label>
                        <Select value={getServiceProviderValue()} onValueChange={handleServiceProviderFilterChange}>
                            <SelectTrigger>
                                <SelectValue placeholder="Select provider availability" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">All Providers</SelectItem>
                                <SelectItem value="with-providers">With Service Providers</SelectItem>
                                <SelectItem value="without-providers">Without Service Providers</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    {/* Sort Options */}
                    <div className="space-y-2">
                        <Label className="text-sm font-medium">Sort By</Label>
                        <Select value={getSortValue()} onValueChange={handleSortChange}>
                            <SelectTrigger>
                                <SelectValue placeholder="Select sort option" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="name-asc">Name (A-Z)</SelectItem>
                                <SelectItem value="name-desc">Name (Z-A)</SelectItem>
                                <SelectItem value="locations-desc">Most Locations</SelectItem>
                                <SelectItem value="locations-asc">Fewest Locations</SelectItem>
                                <SelectItem value="providers-desc">Most Service Providers</SelectItem>
                                <SelectItem value="providers-asc">Fewest Service Providers</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    {/* Applied Filters Summary */}
                    {Object.keys(localFilters).length > 0 && (
                        <div className="space-y-2">
                            <Label className="text-sm font-medium">Active Filters</Label>
                            <div className="space-y-1">
                                {localFilters.locationId && (
                                    <div className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                        Location: {availableLocations.find(loc => loc.id === localFilters.locationId)?.name}
                                    </div>
                                )}
                                {localFilters.hasServiceProviders !== undefined && (
                                    <div className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                        Providers: {localFilters.hasServiceProviders ? 'With Service Providers' : 'Without Service Providers'}
                                    </div>
                                )}
                                {localFilters.sortBy && (
                                    <div className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                        Sort: {localFilters.sortBy} ({localFilters.sortOrder})
                                    </div>
                                )}
                            </div>
                        </div>
                    )}
                </div>

                <SheetFooter className="flex justify-between">
                    <Button variant="outline" onClick={handleReset}>
                        Reset Filters
                    </Button>
                    <div className="flex space-x-2">
                        <Button variant="outline" onClick={() => onOpenChange(false)}>
                            Cancel
                        </Button>
                        <Button onClick={handleApply}>
                            Apply Filters
                        </Button>
                    </div>
                </SheetFooter>
            </SheetContent>
        </Sheet>
    );
} 