import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { servicesApi } from "../api/servicesApi";
import { queryKeys } from "@/lib/query/keys";
import { useOrganizationContext } from "@/features/organizations/context";
import type { CreateServiceRequest } from "../types";

export const useCreateService = () => {
    const queryClient = useQueryClient();
    const { organizationId } = useOrganizationContext();

    return useMutation({
        mutationFn: (data: {
            formData: CreateServiceRequest;
            selectedLocationIds: Set<string>;
            selectedStationIds: Set<string>;
            locationStationMap: Map<string, Set<string>>;
        }) =>
            servicesApi.createService(
                data.formData,
                organizationId!,
                data.selectedLocationIds,
                data.selectedStationIds,
                data.locationStationMap
            ),
        onSuccess: (newService) => {
            // Invalidate services list for current organization
            queryClient.invalidateQueries({
                queryKey: queryKeys.services?.list
                    ? [...queryKeys.services.all, organizationId]
                    : ["services", organizationId],
            });

            toast.success("Service created successfully");
        },
        onError: (error: any) => {
            toast.error(error?.message || "Failed to create service");
        },
    });
};

export const useUpdateService = () => {
    const queryClient = useQueryClient();
    const { organizationId } = useOrganizationContext();

    return useMutation({
        mutationFn: (data: { id: string; updates: Partial<CreateServiceRequest> }) =>
            servicesApi.updateService?.(data.id, data.updates, organizationId!),
        onSuccess: (updatedService) => {
            // Invalidate services list for current organization
            queryClient.invalidateQueries({
                queryKey: queryKeys.services?.list
                    ? [...queryKeys.services.all, organizationId]
                    : ["services", organizationId],
            });

            toast.success("Service updated successfully");
        },
        onError: (error: any) => {
            toast.error(error?.message || "Failed to update service");
        },
    });
};

export const useDeleteService = () => {
    const queryClient = useQueryClient();
    const { organizationId } = useOrganizationContext();

    return useMutation({
        mutationFn: (id: string) => servicesApi.deleteService?.(id, organizationId!),
        onSuccess: () => {
            // Invalidate services list for current organization
            queryClient.invalidateQueries({
                queryKey: queryKeys.services?.list
                    ? [...queryKeys.services.all, organizationId]
                    : ["services", organizationId],
            });

            toast.success("Service deleted successfully");
        },
        onError: (error: any) => {
            toast.error(error?.message || "Failed to delete service");
        },
    });
}; 