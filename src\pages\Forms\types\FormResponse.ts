import * as Types from "../types";

export type GetFormResponsesQueryParams = {
	form_id?: string;
	page?: string;
	per_page?: string;
	uuid?: string;
};

export type GetFormResponsesResponse = {
	success: boolean;
	message: string;
	data: any[];
};

export type Responses = {
	field_id: string;
	value: string;
	value_type: string;
};

export type CreateFormResponses = {
	form_id: string;
	session_id: string;
	status: string;
	responses: Responses[];
	customer_id: string;
};

export type CreateFormResponsesResponse = {
	success: boolean;
	message: string;
	data: any;
};

export type FormResponseDataType = {
	id: string;
	name: string;
	status: string;
	type: string;
	sections: [
		{
			id: string;
			form_id: string;
			title: string;
			description: string;
			flow_action: string;
			flow_target_section_id: string;
			order: number;
			fields: [
				{
					id: string;
					form_section_id: string;
					form_id: string;
					type: string;
					title: string;
					placeholder: string;
					description: string;
					required: boolean;
					image: string;
					info_text_value: string;
					approved_formats: string[];
					date_validation: any;
					selection_limit: number;
					scale_config: any;
					visibility_conditions: any[];
					order: number;
					conditional_defaults: [
						{
							fiend_id: string;
							answer: string;
							default_value: string;
						},
					];
					options: [
						{
							id: string;
							form_field_id: string;
							value: string;
							conditions: any[];
							order: number;
							created_at: string;
							updated_at: string;
						},
					];
					created_at: string;
					updated_at: string;
				},
			];
			created_at: string;
			updated_at: string;
		},
	];
};

export type FormResponseResponseData = {
	success: boolean;
	message: string;
	data: FormResponseDataType;
};

// export type FormResponse = {
// 	id: string;
// 	form_id: string;
// 	status: "in_progress" | "completed" | "blocked";
// 	block_reason?: string;
// 	session_id: string;
// 	customer_id?: string;
// 	submitted_at?: string;
// 	created_at: string;
// 	updated_at: string;
// 	form?: Types.FormTypes.FormDataType; // See Form type in form-endpoints.md
// 	field_responses?: FormFieldResponse[];
// 	customer?: Customer;
// };

export type FormFieldResponse = {
	id: string;
	form_response_id: string;
	field_id: string;
	value: any;
	value_type: string;
	created_at: string;
	updated_at: string;
};

export type Customer = {
	id: string;
	name: string;
};

// API Response Type (what you get from the server)
export type FormResponse = {
	uuid: string;
	form_id: string;
	session_id: string;
	status: "in_progress" | "completed" | "blocked";
	block_reason?: string | null;
	submitted_at: string;
	field_responses: Array<{
		field_id: string;
		value: any;
		value_type: string;
	}>;
	created_at: string;
	updated_at: string;
};

// Update Payload Type (what you send to update)
export type UpdateFormResponsePayload = {
	responses: Array<{
		field_id: string;
		value: any;
	}>;
	user_id?: number;
};
