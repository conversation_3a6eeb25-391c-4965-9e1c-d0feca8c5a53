import { FormTypes, FormResponseTypes } from "../types";
import { FormRenderer } from "./FormRenderer";
import { transformApiResponseToFormResponses } from "../utils/responseTransformers";

interface FormResponseEditorProps {
	formData: FormTypes.FormDataType;
	responseData: FormResponseTypes.FormResponse;
	onSave: (
		updatePayload: FormResponseTypes.UpdateFormResponsePayload
	) => void;
	handleResponseChange: (fieldId: string, value: any) => void;
	mode: "edit" | "view" | "preview";
	userId?: number; // Optional user ID for updates
}

export const FormResponseEditor: React.FC<FormResponseEditorProps> = ({
	formData,
	responseData,
	onSave,
	handleResponseChange,
	mode = "view",
	userId,
}) => {
	// Transform API response to FormResponse format for FormRenderer
	const transformedResponses =
		transformApiResponseToFormResponses(responseData);

	// Handle form submission - convert FormResponse[] back to update payload
	const handleSubmit = (
		updatedResponses: FormResponseTypes.FormResponse[]
	) => {
		// Extract field responses from the FormResponse objects
		const fieldResponses = updatedResponses.flatMap(
			(response) => response.field_responses || []
		);

		// Transform to update payload format
		const updatePayload: FormResponseTypes.UpdateFormResponsePayload = {
			responses: fieldResponses.map((fieldResponse) => ({
				field_id: fieldResponse.field_id,
				value: fieldResponse.value,
			})),
			...(userId && { user_id: userId }),
		};

		onSave(updatePayload);
	};

	return (
		<FormRenderer
			formData={formData}
			responses={transformedResponses}
			mode={mode}
			onResponseChange={handleResponseChange}
			onSubmit={handleSubmit}
		/>
	);
};
