import { useState } from "react";
import * as React from "react";
import {
	Mail,
	MessageSquareText,
	Smartphone,
	GripVertical,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { DualRangeSlider } from "@/components/ui/dual-range-slider";
import { ExpirationSettings } from "@/components/common/ExpirationSettings";
import {
	DndContext,
	closestCenter,
	KeyboardSensor,
	PointerSensor,
	useSensor,
	useSensors,
	type DragEndEvent,
} from "@dnd-kit/core";
import {
	arrayMove,
	SortableContext,
	sortableKeyboardCoordinates,
	verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import {
	useOrganizationScheduleOptimizer,
	useUpdateOrganizationScheduleOptimizer,
} from "@/features/organizations/hooks/useOrganizations";
import {
	useLocationScheduleOptimizer,
	useUpdateLocationScheduleOptimizer,
} from "@/features/locations/hooks/useLocations";
import {
	useStationScheduleOptimizer,
	useUpdateStationScheduleOptimizer,
} from "@/features/locations/hooks/useStations";
import type {
	ScheduleOptimizer,
	UpdateScheduleOptimizerRequest,
	LocationScheduleOptimizerResponse
} from "@/features/organizations/api/organizationsApi";
import Loader from "@/components/Loader";
import { toast } from "sonner";

interface ScheduleOptimizerSettings {
	scheduleOptimizer: boolean;
	newCancellation: boolean;
	cancellationWithin: number;
	cancellationUnit: string;
	openTimeSlot: boolean;
	openSlotBefore: number;
	openSlotUnit: string;
	// Legacy properties for UI compatibility
	autoApprove?: boolean;
	scheduleBlockWeeks?: number;
}

interface GeneralSettings {
	scheduleVisibility: boolean;
	appointmentMethods: {
		inPerson: boolean;
		video: boolean;
		audio: boolean;
	};

	chat: boolean;
	chatMethod: "one-way" | "both-parties";
	scheduleBlock: boolean;
	scheduleBlockDate: Date | null;

	scheduleBlockSpecificDates: boolean;
}

interface DeliveryMethod {
	id: string;
	icon: any;
	label: string;
	enabled: boolean;
}

interface Recipient {
	id: string;
	type: string;
	isSelected: boolean;
	isActive: boolean;
	minValue: number;
	maxValue: number;
	timeUnit: string;
	range: [number, number];
	priority: number;
}

interface SortableRecipientItemProps {
	recipient: Recipient;
	onUpdate: (id: string, updates: Partial<Recipient>) => void;
}

const SortableRecipientItem = ({
	recipient,
	onUpdate,
}: SortableRecipientItemProps) => {
	const {
		attributes,
		listeners,
		setNodeRef,
		transform,
		transition,
		isDragging,
	} = useSortable({ id: recipient.id });

	const style = {
		transform: CSS.Transform.toString(transform),
		transition,
	};

	const recipientTypes = [
		{ value: "walk-in", label: "Walk-in" },
		{ value: "high-priority", label: "High Priority" },
		{ value: "upcoming-appointments", label: "Upcoming Appointments" },
		{ value: "cancelled-appointments", label: "Cancelled Appointments" },
		{ value: "no-show", label: "No Show" },
	];

	const timeUnits = [
		{ value: "hours", label: "Hours away" },
		{ value: "days", label: "Days away" },
		{ value: "weeks", label: "Weeks away" },
		{ value: "months", label: "Months away" },
	];

	const handleRangeChange = (values: number[]) => {
		onUpdate(recipient.id, {
			range: [values[0], values[1]],
			minValue: values[0],
			maxValue: values[1],
		});
	};

	return (
		<div
			ref={setNodeRef}
			style={style}
			className={`relative w-full rounded-[10px] border border-gray-200 bg-white transition-all ${isDragging ? "opacity-50 shadow-lg" : ""
				} ${recipient.isActive
					? "shadow-[0px_0px_4px_0px_rgba(53,154,222,0.25)]"
					: "bg-gray-50"
				}`}
		>
			<div className="relative flex size-full flex-col justify-center">
				<div className="relative box-border flex w-full flex-col content-stretch items-start justify-center gap-4 px-[15px] py-2.5">
					<div className="relative box-border flex w-full flex-row content-stretch items-center justify-between gap-2.5 p-0">
						{/* Drag Handle */}
						<div
							{...attributes}
							{...listeners}
							className={`relative size-6 shrink-0 cursor-grab overflow-clip active:cursor-grabbing ${!recipient.isActive ? "opacity-50" : ""
								}`}
						>
							<GripVertical className="size-6 text-gray-400" />
						</div>

						{/* Checkbox */}
						<div className="relative box-border flex shrink-0 flex-row content-stretch items-center justify-center px-0 py-1">
							<Checkbox
								checked={recipient.isActive}
								onCheckedChange={(checked) =>
									onUpdate(recipient.id, {
										isActive: !!checked,
									})
								}
								className="size-4"
							/>
						</div>

						{/* Recipient Type Dropdown */}
						<span className="w-[185px] text-sm font-medium capitalize">
							{recipient.type}
						</span>

						{/* Min Value Input */}
						<div className="relative flex h-10 shrink-0 rounded-md bg-white">
							<Input
								type="number"
								value={recipient.minValue}
								onChange={(e) =>
									onUpdate(recipient.id, {
										minValue: parseInt(e.target.value) || 0,
										range: [
											parseInt(e.target.value) || 0,
											recipient.maxValue,
										],
									})
								}
								className="h-10 w-[42px] [appearance:textfield] border-gray-200 px-0 py-2 text-center text-sm [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none"
								disabled={!recipient.isActive}
							/>
						</div>

						{/* Range Slider */}
						<div className="relative h-2.5 flex-2 shrink-0">
							<DualRangeSlider
								value={recipient.range}
								onValueChange={handleRangeChange}
								max={100}
								min={0}
								step={1}
								className="w-full px-2"
								disabled={!recipient.isActive}
							/>
						</div>

						{/* Max Value Input */}
						<div className="relative h-10 shrink-0 rounded-md bg-white">
							<Input
								type="number"
								value={recipient.maxValue}
								onChange={(e) =>
									onUpdate(recipient.id, {
										maxValue: parseInt(e.target.value) || 0,
										range: [
											recipient.minValue,
											parseInt(e.target.value) || 0,
										],
									})
								}
								className="h-10 w-[42px] [appearance:textfield] border-gray-200 px-1.5 py-2 text-center text-sm [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none"
								disabled={!recipient.isActive}
							/>
						</div>

						{/* Time Unit Dropdown */}
						{recipient.type === "upcoming-appointments" && (
							<div className="flex flex-row items-center self-stretch">
								<Select
									multiSelect={false}
									value={recipient.timeUnit}
									onValueChange={(
										value: string | string[]
									) => {
										const stringValue = Array.isArray(value)
											? value[0]
											: value;
										onUpdate(recipient.id, {
											timeUnit: stringValue,
										});
									}}
									disabled={!recipient.isActive}
								>
									<SelectTrigger className="h-full w-[140px]">
										<SelectValue />
									</SelectTrigger>
									<SelectContent>
										{timeUnits.map((unit) => (
											<SelectItem
												key={unit.value}
												value={unit.value}
											>
												{unit.label}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</div>
						)}
					</div>
				</div>
			</div>


		</div>
	);
};

// Props to accept context for location/station selection
interface ScheduleOptimizerContentProps {
	selectedLocationId?: string | null;
	selectedStationId?: string | null;
}

export const ScheduleOptimizerContent = ({
	selectedLocationId = null,
	selectedStationId = null
}: ScheduleOptimizerContentProps = {}) => {
	const { organizationId } = useOrganizationContext();

	// Determine which level we're operating at
	const isStationLevel = selectedLocationId && selectedStationId;
	const isLocationLevel = selectedLocationId && !selectedStationId;
	const isOrganizationLevel = !selectedLocationId && !selectedStationId;

	// Context-aware data fetching
	const {
		data: organizationScheduleOptimizer,
		isLoading: isLoadingOrganization,
		error: organizationError,
	} = useOrganizationScheduleOptimizer(organizationId!, !!isOrganizationLevel && !!organizationId);

	const {
		data: locationScheduleOptimizer,
		isLoading: isLoadingLocation,
		error: locationError,
	} = useLocationScheduleOptimizer(selectedLocationId, organizationId!, !!isLocationLevel && !!organizationId);

	const {
		data: stationScheduleOptimizer,
		isLoading: isLoadingStation,
		error: stationError,
	} = useStationScheduleOptimizer(
		selectedLocationId,
		selectedStationId,
		organizationId!,
		!!isStationLevel && !!organizationId
	);

	// Context-aware mutations
	const updateOrganizationMutation = useUpdateOrganizationScheduleOptimizer();
	const updateLocationMutation = useUpdateLocationScheduleOptimizer();
	const updateStationMutation = useUpdateStationScheduleOptimizer();

	// Get the appropriate data and loading state
	const scheduleOptimizerData = isStationLevel
		? (stationScheduleOptimizer as LocationScheduleOptimizerResponse["data"])
		: isLocationLevel
			? (locationScheduleOptimizer as LocationScheduleOptimizerResponse["data"])
			: (organizationScheduleOptimizer as ScheduleOptimizer);

	// Extract inheritance info for location/station
	const inheritanceInfo = isStationLevel
		? (stationScheduleOptimizer as LocationScheduleOptimizerResponse["data"])?.inheritance_info
		: isLocationLevel
			? (locationScheduleOptimizer as LocationScheduleOptimizerResponse["data"])?.inheritance_info
			: null;

	const isLoading = isStationLevel
		? isLoadingStation
		: isLocationLevel
			? isLoadingLocation
			: isLoadingOrganization;

	const error = isStationLevel
		? stationError
		: isLocationLevel
			? locationError
			: organizationError;
	const [settings, setSettings] = useState<ScheduleOptimizerSettings>({
		scheduleOptimizer: true,
		newCancellation: true,
		cancellationWithin: 24,
		cancellationUnit: "hours",
		openTimeSlot: false,
		openSlotBefore: 2,
		openSlotUnit: "hours",
		// Legacy properties for UI compatibility
		autoApprove: false,
		scheduleBlockWeeks: 4,
	});

	const [deliveryMethods, setDeliveryMethods] = useState<DeliveryMethod[]>([
		{ id: "email", icon: Mail, label: "Email", enabled: true },
		{ id: "sms", icon: Smartphone, label: "SMS", enabled: false },
		{ id: "in-app", icon: MessageSquareText, label: "In-App", enabled: false },
	]);

	const [recipients, setRecipients] = useState<Recipient[]>([
		{
			id: "1",
			type: "walk-in",
			isSelected: true,
			isActive: true,
			minValue: 10,
			maxValue: 30,
			timeUnit: "hours",
			range: [10, 30],
			priority: 1,
		},
		{
			id: "2",
			type: "high-priority",
			isSelected: true,
			isActive: true,
			minValue: 10,
			maxValue: 30,
			timeUnit: "hours",
			range: [10, 30],
			priority: 2,
		},
		{
			id: "3",
			type: "upcoming-appointments",
			isSelected: false,
			isActive: false,
			minValue: 2,
			maxValue: 4,
			timeUnit: "weeks",
			range: [2, 4],
			priority: 3,
		},
	]);

	const sensors = useSensors(
		useSensor(PointerSensor),
		useSensor(KeyboardSensor, {
			coordinateGetter: sortableKeyboardCoordinates,
		})
	);

	const updateSettings = (updates: Partial<ScheduleOptimizerSettings>) => {
		setSettings((prev) => ({ ...prev, ...updates }));
	};

	const updateRecipient = (id: string, updates: Partial<Recipient>) => {
		setRecipients((prev) =>
			prev.map((recipient) =>
				recipient.id === id ? { ...recipient, ...updates } : recipient
			)
		);
	};

	// Update local state when API data loads
	React.useEffect(() => {
		if (scheduleOptimizerData) {
			setSettings({
				scheduleOptimizer: scheduleOptimizerData.is_enabled,
				newCancellation: scheduleOptimizerData.trigger.new_cancellation.is_enabled,
				cancellationWithin: scheduleOptimizerData.trigger.new_cancellation.within_value,
				cancellationUnit: scheduleOptimizerData.trigger.new_cancellation.within_unit,
				openTimeSlot: scheduleOptimizerData.trigger.open_time_slot.is_enabled,
				openSlotBefore: scheduleOptimizerData.trigger.open_time_slot.before_value,
				openSlotUnit: scheduleOptimizerData.trigger.open_time_slot.before_unit,
				// Legacy properties for UI compatibility
				autoApprove: false,
				scheduleBlockWeeks: 4,
			});

			// Update delivery methods
			setDeliveryMethods([
				{ id: "email", icon: Mail, label: "Email", enabled: scheduleOptimizerData.delivery.email },
				{ id: "sms", icon: Smartphone, label: "SMS", enabled: scheduleOptimizerData.delivery.sms },
				{ id: "in-app", icon: MessageSquareText, label: "In-App", enabled: scheduleOptimizerData.delivery.in_app },
			]);

			// Update recipients and maintain priority based on API data or current order
			setRecipients(prevRecipients => {
				const updatedRecipients = prevRecipients.map(recipient => {
					if (recipient.id === "1") {
						return {
							...recipient,
							isActive: scheduleOptimizerData.recipients.walk_in.is_enabled,
							minValue: scheduleOptimizerData.recipients.walk_in.priority_min,
							maxValue: scheduleOptimizerData.recipients.walk_in.priority_max,
							range: [
								scheduleOptimizerData.recipients.walk_in.priority_min,
								scheduleOptimizerData.recipients.walk_in.priority_max
							] as [number, number],
							priority: scheduleOptimizerData.recipients.walk_in.priority || recipient.priority
						};
					} else if (recipient.id === "2") {
						return {
							...recipient,
							isActive: scheduleOptimizerData.recipients.high_priority.is_enabled,
							minValue: scheduleOptimizerData.recipients.high_priority.priority_min,
							maxValue: scheduleOptimizerData.recipients.high_priority.priority_max,
							range: [
								scheduleOptimizerData.recipients.high_priority.priority_min,
								scheduleOptimizerData.recipients.high_priority.priority_max
							] as [number, number],
							priority: scheduleOptimizerData.recipients.high_priority.priority || recipient.priority
						};
					} else if (recipient.id === "3") {
						return {
							...recipient,
							isActive: scheduleOptimizerData.recipients.upcoming_appointments.is_enabled,
							minValue: scheduleOptimizerData.recipients.upcoming_appointments.min_value,
							maxValue: scheduleOptimizerData.recipients.upcoming_appointments.max_value,
							range: [
								scheduleOptimizerData.recipients.upcoming_appointments.min_value,
								scheduleOptimizerData.recipients.upcoming_appointments.max_value
							] as [number, number],
							priority: scheduleOptimizerData.recipients.upcoming_appointments.priority || recipient.priority
						};
					}
					return recipient;
				});

				// Sort by priority to maintain correct order
				return updatedRecipients.sort((a, b) => a.priority - b.priority);
			});
		}
	}, [scheduleOptimizerData]);

	const handleDragEnd = (event: DragEndEvent) => {
		const { active, over } = event;

		if (active.id !== over?.id) {
			setRecipients((items) => {
				const oldIndex = items.findIndex(
					(item) => item.id === active.id
				);
				const newIndex = items.findIndex(
					(item) => item.id === over?.id
				);

				const reorderedItems = arrayMove(items, oldIndex, newIndex);

				// Update priorities based on new positions
				return reorderedItems.map((item, index) => ({
					...item,
					priority: index + 1
				}));
			});
		}
	};

	const handleSave = async () => {
		if (!organizationId) {
			toast.error("Organization ID is required");
			return;
		}

		try {
			const updateData: UpdateScheduleOptimizerRequest = {
				is_enabled: settings.scheduleOptimizer,
				trigger: {
					new_cancellation: {
						is_enabled: settings.newCancellation,
						within_value: settings.cancellationWithin,
						within_unit: settings.cancellationUnit as "hours" | "days" | "weeks",
					},
					open_time_slot: {
						is_enabled: settings.openTimeSlot,
						before_value: settings.openSlotBefore,
						before_unit: settings.openSlotUnit as "hours" | "days" | "weeks",
					},
				},
				recipients: {
					walk_in: {
						is_enabled: recipients.find(r => r.id === "1")?.isActive || false,
						priority_min: recipients.find(r => r.id === "1")?.range[0] || 10,
						priority_max: recipients.find(r => r.id === "1")?.range[1] || 30,
						priority: recipients.find(r => r.id === "1")?.priority || 1,
					},
					high_priority: {
						is_enabled: recipients.find(r => r.id === "2")?.isActive || false,
						priority_min: recipients.find(r => r.id === "2")?.range[0] || 10,
						priority_max: recipients.find(r => r.id === "2")?.range[1] || 30,
						priority: recipients.find(r => r.id === "2")?.priority || 2,
					},
					upcoming_appointments: {
						is_enabled: recipients.find(r => r.id === "3")?.isActive || false,
						min_value: recipients.find(r => r.id === "3")?.minValue || 2,
						max_value: recipients.find(r => r.id === "3")?.maxValue || 4,
						unit: "weeks",
						priority: recipients.find(r => r.id === "3")?.priority || 3,
					},
				},
				delivery: {
					email: deliveryMethods.find(d => d.id === "email")?.enabled || false,
					sms: deliveryMethods.find(d => d.id === "sms")?.enabled || false,
					in_app: deliveryMethods.find(d => d.id === "in-app")?.enabled || false,
				},
			};

			if (isStationLevel && selectedLocationId && selectedStationId) {
				await updateStationMutation.mutateAsync({
					locationId: selectedLocationId,
					stationId: selectedStationId,
					organizationId: organizationId,
					data: updateData,
				});
			} else if (isLocationLevel && selectedLocationId) {
				await updateLocationMutation.mutateAsync({
					locationId: selectedLocationId,
					organizationId: organizationId,
					data: updateData,
				});
			} else {
				await updateOrganizationMutation.mutateAsync({
					organizationId: organizationId,
					data: updateData,
				});
			}
		} catch (error) {
			console.error("Failed to save schedule optimizer:", error);
			toast.error("Failed to save schedule optimizer settings");
		}
	};



	// Get inheritance message
	const getInheritanceMessage = () => {
		if (!inheritanceInfo) return null;

		if (inheritanceInfo.has_own_optimizer) {
			return {
				type: "custom" as const,
				message: isStationLevel
					? "This station has custom schedule optimizer settings"
					: "This location has custom schedule optimizer settings"
			};
		} else {
			const source = inheritanceInfo.inheriting_from === "organization" ? "organization" : "location";
			return {
				type: "inherited" as const,
				message: isStationLevel
					? `This station is inheriting schedule optimizer settings from the ${source}`
					: `This location is inheriting schedule optimizer settings from the ${source}`
			};
		}
	};

	const inheritanceMessage = getInheritanceMessage();

	if (isLoading) {
		return (
			<div className="flex items-center justify-center py-8">
				<Loader />
			</div>
		);
	}

	if (error) {
		return (
			<div className="text-red-600 text-center py-4">
				Failed to load schedule optimizer. Please try again.
			</div>
		);
	}

	return (
		<div className="space-y-4">
			{/* Inheritance Info Banner */}
			{inheritanceMessage && (
				<div className={`p-3 rounded-lg border ${inheritanceMessage.type === "inherited"
					? "bg-blue-50 border-blue-200 text-blue-800"
					: "bg-green-50 border-green-200 text-green-800"
					}`}>
					<div className="flex items-center gap-2">
						{inheritanceMessage.type === "inherited" ? (
							<svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
								<path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
							</svg>
						) : (
							<svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
								<path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
							</svg>
						)}
						<span className="font-medium text-sm">
							{inheritanceMessage.message}
						</span>
					</div>
					{inheritanceMessage.type === "inherited" && (
						<p className="mt-2 text-sm opacity-80">
							Any changes you make here will create custom schedule optimizer settings for this {isStationLevel ? 'station' : 'location'}.
						</p>
					)}
				</div>
			)}
			<p className="border-b border-gray-200 pb-4 text-sm text-gray-600">
				When this feature is on, we alert selected patients of open time
				slots
			</p>

			{/* Schedule Optimizer Trigger(s) */}
			<div className="space-y-3 border-b border-gray-200 pb-4">
				<div className="flex flex-1 flex-col items-start justify-between space-y-4">
					<div className="space-y-1">
						<h3 className="text-lg font-medium">
							Schedule Optimizer Trigger(s)
						</h3>
						<p className="text-sm text-gray-600">
							Select which actions would cause the Schedule
							Optimizer to be actived
						</p>
					</div>
					<div className="w-full flex-1 space-y-3 pl-6">
						<div className="flex items-center justify-between">
							<div className="space-y-1">
								<h3 className="text-lg font-medium">
									New Cancellation
								</h3>
								<p className="text-sm text-gray-600">
									If there is a cancellation, selected
									patients will be notified of an open time
									slot.
								</p>
							</div>
							<div className="flex items-center gap-1.5">
								<Switch
									checked={settings.newCancellation}
									onCheckedChange={(checked) =>
										updateSettings({ newCancellation: checked })
									}
								/>
								<span className="text-muted">
									{settings.newCancellation ? "On" : "Off"}
								</span>
							</div>
						</div>
						<div className="flex flex-col items-end gap-2.5">
							<div className="flex items-center gap-2.5">
								<span className="text-sm font-medium">
									Within
								</span>
								<ExpirationSettings
									value={settings.cancellationWithin.toString()}
									onValueChange={(value) =>
										updateSettings({
											cancellationWithin: parseInt(value),
										})
									}
									onUnitChange={() => { }}
									unit="weeks"
									useSpecificDate={false}
									label=""
									units={[
										{ value: "hours", label: "Hours" },
										{
											value: "minutes",
											label: "Minutes",
										},
										{ value: "weeks", label: "Weeks" },
										{ value: "days", label: "Days" },
									]}
									showAlternativeOption={false}
									containerWidth="w-[145px]"
								/>
							</div>
						</div>
					</div>
					<div className="w-full flex-1 space-y-3 pl-6">
						<div className="flex items-center justify-between">
							<div className="space-y-1">
								<h3 className="text-lg font-medium">
									Open Time Slot
								</h3>
								<p className="text-sm text-gray-600">
									If there is still an open time slot, send
									out an email
								</p>
							</div>
							<div className="flex items-center gap-1.5">
								<Switch
									checked={settings.openTimeSlot}
									onCheckedChange={(checked) =>
										updateSettings({ openTimeSlot: checked })
									}
								/>
								<span className="text-muted">
									{settings.openTimeSlot ? "On" : "Off"}
								</span>
							</div>
						</div>
						<div className="flex flex-col items-end gap-2.5">
							<div className="flex items-center gap-2.5">
								<span className="text-sm font-medium">
									Select Time
								</span>
								<ExpirationSettings
									value={settings.openSlotBefore.toString()}
									onValueChange={(value) =>
										updateSettings({
											openSlotBefore: parseInt(value),
										})
									}
									onUnitChange={() => { }}
									unit="weeks"
									useSpecificDate={false}
									label=""
									units={[
										{ value: "hours", label: "Hours" },
										{
											value: "minutes",
											label: "Minutes",
										},
										{ value: "weeks", label: "Weeks" },
										{ value: "days", label: "Days" },
									]}
									showAlternativeOption={false}
									containerWidth="w-[145px]"
								/>
								<span className="text-sm font-medium">
									Before
								</span>
							</div>
						</div>
					</div>
				</div>
			</div>

			{/* Select Recipients */}
			<div className="space-y-4 border-b border-gray-200 pb-4">
				<div className="flex items-center justify-between">
					<div className="space-y-1">
						<h3 className="text-lg font-medium">
							Select Recipients
						</h3>
						<p className="text-sm text-gray-600">
							[description here]....Select order to send by
							priority.
						</p>
					</div>
				</div>

				<div className="ml-0">
					<DndContext
						sensors={sensors}
						collisionDetection={closestCenter}
						onDragEnd={handleDragEnd}
					>
						<SortableContext
							items={recipients.map((r) => r.id)}
							strategy={verticalListSortingStrategy}
						>
							<div className="space-y-2">
								{recipients.map((recipient) => (
									<SortableRecipientItem
										key={recipient.id}
										recipient={recipient}
										onUpdate={updateRecipient}
									/>
								))}
							</div>
						</SortableContext>
					</DndContext>
				</div>
			</div>

			{/* Method of Delivery */}
			<div className="space-y-4 pb-4">
				<h3 className="text-lg font-medium">Method of Delivery</h3>
				<div className="flex gap-6">
					<Button asChild variant="outline">
						<div className="flex items-center space-x-2">
							<Label
								htmlFor="in-person"
								className="flex items-center gap-2 text-sm font-medium"
							>
								<Mail className="h-4 w-4" />
								Email
							</Label>
							<Checkbox
								id="email"
								checked={deliveryMethods.find(d => d.id === "email")?.enabled || false}
								onCheckedChange={(checked) => {
									setDeliveryMethods(prev =>
										prev.map(d =>
											d.id === "email" ? { ...d, enabled: !!checked } : d
										)
									)
								}}
							/>
						</div>
					</Button>
					<Button asChild variant="outline">
						<div className="flex items-center space-x-2">
							<Label
								htmlFor="video"
								className="flex items-center gap-2 text-sm font-medium"
							>
								<MessageSquareText className="h-4 w-4" />
								SMS
							</Label>
							<Checkbox
								id="sms"
								checked={deliveryMethods.find(d => d.id === "sms")?.enabled || false}
								onCheckedChange={(checked) => {
									setDeliveryMethods(prev =>
										prev.map(d =>
											d.id === "sms" ? { ...d, enabled: !!checked } : d
										)
									)
								}}
							/>
						</div>
					</Button>
					<Button asChild variant="outline">
						<div className="flex items-center space-x-2">
							<Label
								htmlFor="audio"
								className="flex items-center gap-2 text-sm font-medium"
							>
								<Smartphone className="h-4 w-4" />
								In-App
							</Label>
							<Checkbox
								id="in-app"
								checked={deliveryMethods.find(d => d.id === "in-app")?.enabled || false}
								onCheckedChange={(checked) => {
									setDeliveryMethods(prev =>
										prev.map(d =>
											d.id === "in-app" ? { ...d, enabled: !!checked } : d
										)
									)
								}}
							/>
						</div>
					</Button>
				</div>
			</div>

			{/* Action Buttons */}
			<div className="flex justify-end gap-3 pt-6">
				<Button variant="outline">Cancel</Button>
				<Button
					onClick={handleSave}
					disabled={
						updateOrganizationMutation.isPending ||
						updateLocationMutation.isPending ||
						updateStationMutation.isPending
					}
				>
					{(updateOrganizationMutation.isPending ||
						updateLocationMutation.isPending ||
						updateStationMutation.isPending) && "Saving..."}
					{!(updateOrganizationMutation.isPending ||
						updateLocationMutation.isPending ||
						updateStationMutation.isPending) && "Save"}
				</Button>
			</div>
		</div>
	);
};
