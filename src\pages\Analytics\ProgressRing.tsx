import React, { useEffect, useState } from "react";
import { motion } from "framer-motion";

const ProgressRing = ({ progress, colors }) => {
	const [isAnimating, setIsAnimating] = useState(false);
	const size = 75;
	const strokeWidth = 12;
	const radius = (size - strokeWidth) / 2;
	const circumference = radius * 2 * Math.PI;
	const offset = circumference - (progress / 100) * circumference;
	const rotation = -90;

	useEffect(() => {
		setIsAnimating(true);
	}, [progress]);

	return (
		<div className="relative h-[90px] w-[90px]">
			{/* Background circle */}
			<svg
				width={size}
				height={size}
				viewBox={`0 0 ${size} ${size}`}
				style={{ transform: `rotate(${rotation}deg)` }}
			>
				<circle
					cx={size / 2}
					cy={size / 2}
					r={radius}
					fill="none"
					stroke={colors.dim}
					strokeWidth={strokeWidth}
				/>
			</svg>

			{/* Progress circle */}
			<svg
				width={size}
				height={size}
				viewBox={`0 0 ${size} ${size}`}
				style={{
					transform: `rotate(${rotation}deg)`,
					position: "absolute",
					top: 0,
					left: 0,
				}}
			>
				<motion.circle
					cx={size / 2}
					cy={size / 2}
					r={radius}
					fill="none"
					stroke={colors.dark}
					strokeWidth={strokeWidth}
					strokeLinecap="round"
					initial={{
						strokeDasharray: circumference,
						strokeDashoffset: circumference,
					}}
					animate={{
						strokeDasharray: circumference,
						strokeDashoffset: offset,
					}}
					transition={{
						duration: 1,
						ease: "easeOut",
					}}
				/>
			</svg>
		</div>
	);
};

export default ProgressRing;
