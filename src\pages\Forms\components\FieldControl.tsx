import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { IoAddCircleOutline } from "react-icons/io5";
import { PiFileArrowUp, PiImageSquare } from "react-icons/pi";
import { Type } from "lucide-react";
import { LuStretchHorizontal } from "react-icons/lu";
import {
	TooltipProvider,
	Tooltip,
	TooltipContent,
	TooltipTrigger,
} from "@/components/ui/tooltip";
import type { FormTypes } from "@/pages/Forms/types";

interface FieldControlProps {
	sectionIndex: number;
	onAddField: (sectionIndex: number, type: FormTypes.FieldType) => void;
	onAddSection: () => void;
	isVisible: boolean;
	position?: "field" | "section";
}

export const FieldControl: React.FC<FieldControlProps> = ({
	sectionIndex,
	onAddField,
	onAddSection,
	isVisible,
	position = "section",
}) => {
	if (!isVisible) return null;

	const positionClass =
		position === "field"
			? "absolute -right-17 top-1/2 -translate-y-1/2"
			: "absolute -right-17 top-0";

	return (
		<div
			className={`${positionClass} !mt-0 flex flex-col items-center justify-center gap-2`}
		>
			<TooltipProvider>
				<div className="border-stroke-weak bg-foreground-muted flex flex-col gap-2 rounded-xl border p-3">
					<Tooltip delayDuration={0}>
						<TooltipTrigger asChild>
							<Button
								type="button"
								variant="outline"
								className="text-muted h-8 w-8 cursor-pointer rounded-md border-none p-2 hover:bg-green-700 hover:text-white"
								onClick={() => onAddField(sectionIndex, "text")}
							>
								<IoAddCircleOutline size={20} />
							</Button>
						</TooltipTrigger>
						<TooltipContent side="left" sideOffset={10}>
							Add Field
						</TooltipContent>
					</Tooltip>

					<Tooltip delayDuration={0}>
						<TooltipTrigger asChild>
							<Button
								type="button"
								variant="outline"
								className="text-muted h-8 w-8 cursor-pointer rounded-md border-none p-2 hover:bg-green-700 hover:text-white"
								onClick={() =>
									onAddField(sectionIndex, "attachment")
								}
							>
								<PiFileArrowUp size={20} />
							</Button>
						</TooltipTrigger>
						<TooltipContent side="left" sideOffset={10}>
							Add Attachment
						</TooltipContent>
					</Tooltip>

					<Tooltip delayDuration={0}>
						<TooltipTrigger asChild>
							<Button
								type="button"
								variant="outline"
								className="text-muted h-8 w-8 cursor-pointer rounded-md border-none p-2 hover:bg-green-700 hover:text-white"
								onClick={() =>
									onAddField(sectionIndex, "longtext")
								}
							>
								<Type size={20} />
							</Button>
						</TooltipTrigger>
						<TooltipContent side="left" sideOffset={10}>
							Add Long Text
						</TooltipContent>
					</Tooltip>

					<Tooltip delayDuration={0}>
						<TooltipTrigger asChild>
							<Button
								type="button"
								variant="outline"
								className="text-muted h-8 w-8 cursor-pointer rounded-md border-none p-2 hover:bg-green-700 hover:text-white"
								onClick={() =>
									onAddField(sectionIndex, "info_image")
								}
							>
								<PiImageSquare size={20} />
							</Button>
						</TooltipTrigger>
						<TooltipContent side="left" sideOffset={10}>
							Add Informational Image
						</TooltipContent>
					</Tooltip>
				</div>

				<div className="bg-foreground-muted border-stroke-weak flex flex-col gap-2 rounded-xl border p-3">
					<Tooltip delayDuration={0}>
						<TooltipTrigger asChild>
							<Button
								type="button"
								variant="outline"
								className="text-muted h-8 w-8 cursor-pointer rounded-md border-none p-2 hover:bg-green-700 hover:text-white"
								onClick={onAddSection}
							>
								<LuStretchHorizontal size={20} />
							</Button>
						</TooltipTrigger>
						<TooltipContent side="left" sideOffset={10}>
							Add Section
						</TooltipContent>
					</Tooltip>
				</div>
			</TooltipProvider>
		</div>
	);
};
