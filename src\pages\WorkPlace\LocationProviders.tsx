import { useEffect, useState, type FC } from "react";
import { useParams } from "react-router";
import { LocationDetailsCard } from "@/features/locations";
import { LocationProvidersList } from "@/features/providers";
import { AddStationSheet } from "@/features/locations/components/sheets";
import { useUIStore } from "@/stores/uiStore";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import { locationsApi } from "@/features/locations/api";
import { StationProvider } from "@/features/locations/hooks";
import type { LocationDetails } from "@/features/locations/components/LocationDetailsCard";
import type {
	Location,
	CreateProviderStationRequest,
} from "@/features/locations/types";
import LocationDetailsSheet from "@/features/locations/components/sheets/location-details/LocationDetailsSheet";
import { Skeleton } from "@/components/ui/skeleton";

const LocationProviders: FC = () => {
	const { providerId } = useParams<{ providerId: string }>();
	const { organizationId } = useOrganizationContext();
	const setBreadcrumbs = useUIStore((state) => state.setBreadcrumbs);
	const setCurrentPageTitle = useUIStore(
		(state) => state.setCurrentPageTitle
	);

	// State management
	const [location, setLocation] = useState<Location | null>(null);
	const [isLoadingLocation, setIsLoadingLocation] = useState(false);
	const [locationError, setLocationError] = useState<string | null>(null);
	const [openOrganizationDetailsSheet, setOpenOrganizationDetailsSheet] =
		useState(false);
	const [openAddStationSheet, setOpenAddStationSheet] = useState(false);

	// Fetch location data
	useEffect(() => {
		const fetchLocation = async () => {
			if (!providerId || !organizationId) return;

			setIsLoadingLocation(true);
			setLocationError(null);
			try {
				const locationData = await locationsApi.getLocation(
					providerId,
					organizationId
				);
				console.log("Fetched location data:", locationData);
				setLocation(locationData);
			} catch (error) {
				console.error("Error fetching location:", error);
				setLocationError("Failed to load location data");
			} finally {
				setIsLoadingLocation(false);
			}
		};

		fetchLocation();
	}, [providerId, organizationId]);

	// Set breadcrumbs when component mounts
	useEffect(() => {
		setBreadcrumbs([
			{
				label: "Workplace",
				href: "/dashboard/workplace",
			},
			{
				label: "Organizations",
				href: "/dashboard/workplace/locations",
			},
			{
				label: location?.name || "Providers",
				isCurrentPage: true,
			},
		]);
		setCurrentPageTitle(
			location ? `${location.name} - Providers` : "Location & Providers"
		);

		// Cleanup breadcrumbs when component unmounts
		return () => {
			setBreadcrumbs([]);
		};
	}, [setBreadcrumbs, setCurrentPageTitle, location]);

	const handleView = (location: LocationDetails) => {
		console.log("View:", location.name);
		setOpenOrganizationDetailsSheet(true);
	};

	const handleAddStation = () => {
		setOpenAddStationSheet(true);
	};

	const handleStationSubmit = async (
		providerData: CreateProviderStationRequest & { imageFile?: File }
	) => {
		try {
			if (!providerId || !organizationId) {
				console.error("Missing providerId or organizationId");
				return;
			}

			console.log("Creating provider station:", providerData);

			// The StationContext will handle the API call and refetch
			setOpenAddStationSheet(false);

			// You might want to add a success toast here
		} catch (error) {
			console.error("Error creating station:", error);
			// You might want to add an error toast here
		}
	};

	// Show loading state
	if (isLoadingLocation) {
		return (
			<div className="flex flex-col gap-4 py-6">
				{/* LocationDetailsCard Skeleton */}
				<div className="rounded-lg border border-gray-200 bg-white p-6">
					<div className="flex items-start justify-between">
						<div className="flex-1">
							<div className="mb-2 flex items-center gap-3">
								<Skeleton className="h-8 w-56" />
								<Skeleton className="h-6 w-12 rounded-full" />
							</div>
							<Skeleton className="mb-4 h-4 w-80" />
							<div className="flex items-center gap-8">
								<div className="flex items-center gap-2">
									<Skeleton className="h-4 w-4" />
									<Skeleton className="h-4 w-24" />
								</div>
								<div className="flex items-center gap-2">
									<Skeleton className="h-4 w-4" />
									<Skeleton className="h-4 w-28" />
								</div>
								<div className="flex items-center gap-2">
									<Skeleton className="h-4 w-4" />
									<Skeleton className="h-4 w-20" />
								</div>
							</div>
						</div>
						<div className="flex items-center gap-2">
							<Skeleton className="h-9 w-16" />
							<Skeleton className="h-9 w-14" />
							<Skeleton className="h-9 w-18" />
						</div>
					</div>
				</div>

				{/* LocationProvidersList Skeleton */}
				<div className="rounded-lg border border-gray-200 bg-white">
					{/* Header */}
					<div className="border-b border-gray-200 p-6">
						<div className="flex items-center justify-between">
							<div>
								<Skeleton className="mb-2 h-7 w-40" />
								<Skeleton className="h-4 w-64" />
							</div>
							<Skeleton className="h-10 w-32" />
						</div>
					</div>

					{/* Tabs */}
					<div className="border-b border-gray-200 px-6">
						<div className="flex space-x-8 py-2">
							<Skeleton className="h-8 w-16" />
							<Skeleton className="h-8 w-20" />
							<Skeleton className="h-8 w-18" />
						</div>
					</div>

					{/* Content Area */}
					<div className="p-6">
						<div className="space-y-3">
							{Array.from({ length: 4 }).map((_, index) => (
								<div
									key={index}
									className="rounded-lg border border-gray-100 p-4"
								>
									<div className="flex items-center justify-between">
										<div className="flex items-center gap-4">
											<Skeleton className="h-12 w-12 rounded-full" />
											<div className="space-y-2">
												<Skeleton className="h-5 w-36" />
												<Skeleton className="h-3 w-28" />
											</div>
										</div>
										<div className="flex items-center gap-3">
											<Skeleton className="h-7 w-18 rounded-full" />
											<Skeleton className="h-8 w-8 rounded" />
										</div>
									</div>
								</div>
							))}
						</div>
					</div>
				</div>
			</div>
		);
	}

	// Show error state
	if (locationError || !location) {
		return (
			<div className="flex flex-col gap-4 py-6">
				<div className="flex items-center justify-center py-8">
					<span className="text-sm text-red-500">
						{locationError || "Location not found"}
					</span>
				</div>
			</div>
		);
	}

	return (
		<StationProvider
			locationId={providerId}
			organizationId={organizationId || undefined}
		>
			<LocationProvidersContent
				location={location}
				isLoadingLocation={isLoadingLocation}
				locationError={locationError}
				providerId={providerId}
				organizationId={organizationId!}
				handleView={handleView}
				handleAddStation={handleAddStation}
				handleStationSubmit={handleStationSubmit}
				openOrganizationDetailsSheet={openOrganizationDetailsSheet}
				setOpenOrganizationDetailsSheet={
					setOpenOrganizationDetailsSheet
				}
				openAddStationSheet={openAddStationSheet}
				setOpenAddStationSheet={setOpenAddStationSheet}
			/>
		</StationProvider>
	);
};

interface LocationProvidersContentProps {
	location: Location | null;
	isLoadingLocation: boolean;
	locationError: string | null;
	providerId?: string;
	organizationId?: number;
	handleView: (location: LocationDetails) => void;
	handleAddStation: () => Promise<void>;
	handleStationSubmit: (
		providerData:
			| (CreateProviderStationRequest & { imageFile?: File })
			| { name: string; description?: string }
	) => Promise<void>;
	openOrganizationDetailsSheet: boolean;
	setOpenOrganizationDetailsSheet: (open: boolean) => void;
	openAddStationSheet: boolean;
	setOpenAddStationSheet: (open: boolean) => void;
}

const LocationProvidersContent: FC<LocationProvidersContentProps> = ({
	location,
	isLoadingLocation,
	locationError,
	providerId,
	organizationId,
	handleView,
	handleAddStation,
	handleStationSubmit,
	openOrganizationDetailsSheet,
	setOpenOrganizationDetailsSheet,
	openAddStationSheet,
	setOpenAddStationSheet,
}) => {
	// Show loading state
	if (isLoadingLocation) {
		return (
			<div className="flex flex-col gap-4 py-6">
				{/* LocationDetailsCard Skeleton */}
				<div className="rounded-lg border border-gray-200 bg-white p-6">
					<div className="flex items-start justify-between">
						<div className="flex-1">
							<div className="mb-2 flex items-center gap-3">
								<Skeleton className="h-8 w-56" />
								<Skeleton className="h-6 w-12 rounded-full" />
							</div>
							<Skeleton className="mb-4 h-4 w-80" />
							<div className="flex items-center gap-8">
								<div className="flex items-center gap-2">
									<Skeleton className="h-4 w-4" />
									<Skeleton className="h-4 w-24" />
								</div>
								<div className="flex items-center gap-2">
									<Skeleton className="h-4 w-4" />
									<Skeleton className="h-4 w-28" />
								</div>
								<div className="flex items-center gap-2">
									<Skeleton className="h-4 w-4" />
									<Skeleton className="h-4 w-20" />
								</div>
							</div>
						</div>
						<div className="flex items-center gap-2">
							<Skeleton className="h-9 w-16" />
							<Skeleton className="h-9 w-14" />
							<Skeleton className="h-9 w-18" />
						</div>
					</div>
				</div>

				{/* LocationProvidersList Skeleton */}
				<div className="rounded-lg border border-gray-200 bg-white">
					{/* Header */}
					<div className="border-b border-gray-200 p-6">
						<div className="flex items-center justify-between">
							<div>
								<Skeleton className="mb-2 h-7 w-40" />
								<Skeleton className="h-4 w-64" />
							</div>
							<Skeleton className="h-10 w-32" />
						</div>
					</div>

					{/* Tabs */}
					<div className="border-b border-gray-200 px-6">
						<div className="flex space-x-8 py-2">
							<Skeleton className="h-8 w-16" />
							<Skeleton className="h-8 w-20" />
							<Skeleton className="h-8 w-18" />
						</div>
					</div>

					{/* Content Area */}
					<div className="p-6">
						<div className="space-y-3">
							{Array.from({ length: 4 }).map((_, index) => (
								<div
									key={index}
									className="rounded-lg border border-gray-100 p-4"
								>
									<div className="flex items-center justify-between">
										<div className="flex items-center gap-4">
											<Skeleton className="h-12 w-12 rounded-full" />
											<div className="space-y-2">
												<Skeleton className="h-5 w-36" />
												<Skeleton className="h-3 w-28" />
											</div>
										</div>
										<div className="flex items-center gap-3">
											<Skeleton className="h-7 w-18 rounded-full" />
											<Skeleton className="h-8 w-8 rounded" />
										</div>
									</div>
								</div>
							))}
						</div>
					</div>
				</div>
			</div>
		);
	}

	// Show error state
	if (locationError || !location) {
		return (
			<div className="flex flex-col gap-4 py-6">
				<div className="flex items-center justify-center py-8">
					<span className="text-sm text-red-500">
						{locationError || "Location not found"}
					</span>
				</div>
			</div>
		);
	}

	return (
		<div className="flex flex-col gap-4 py-6">
			<LocationDetailsCard
				location={location}
				onView={handleView}
				onEdit={(location) => console.log("Edit:", location.name)}
				onDelete={(location) => console.log("Delete:", location.name)}
			/>
			<LocationProvidersList onAddStation={handleAddStation} />
			<LocationDetailsSheet
				open={openOrganizationDetailsSheet}
				onClose={() => setOpenOrganizationDetailsSheet(false)}
				locationId={providerId!}
			/>
			<AddStationSheet
				open={openAddStationSheet}
				onOpenChange={setOpenAddStationSheet}
				onSubmit={handleStationSubmit}
				locationId={providerId}
				organizationId={organizationId || undefined}
			/>
		</div>
	);
};

export default LocationProviders;
