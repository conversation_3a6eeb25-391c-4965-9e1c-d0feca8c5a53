import Bar<PERSON>hartCustom from "./BarChartCustom";
import Ratings<PERSON>hart from "./RatingsChart";
// import { AnalyticsApiResponse } from "@/src/types/Analytics";/
import { forwardRef } from "react";
import StatisticsCard from "./StatisticsCard";
import Loader from "@/components/Loader";
import type { UseQueryResult } from "@tanstack/react-query";
import { dummyAnalyticsData } from './data';

const StatisticsCharts = forwardRef<
	HTMLDivElement[],
	{
		analyticsData: any;
		isLoading: boolean;
	}
>(({ analyticsData, isLoading }, ref) => {
	const colors = [
		"#F7E8B1",
		"#F3AC8C",
		"#A142D8",
		"#89B7EE",
	];

	console.log(analyticsData)

	const generateCategories = (categoryKeys: string[]) => {
		if (!categoryKeys) return;

		const categoryFormats: any = {
			no_show: "No Shows",
			scheduled: "Scheduled",
			walk_in: "Walk-in",
			cancelled: "Cancelled",
		};

		const categoryColors: any = {
			scheduled: "#005893",
			walk_in: "#D6E4EE",
			cancelled: "#D8424B",
			no_show: "#979797",
		};

		return categoryKeys.reduce((acc: any, key: any) => {
			acc[key] = {
				label:
					categoryFormats[key] ||
					key.charAt(0).toUpperCase() +
						key
							.slice(1)
							.replace(/([A-Z])/g, " $1")
							.trim(),
				color:
					categoryColors[key] ||
					colors[Object.keys(acc).length % colors.length],
			};
			return acc;
		}, {});
	};

	if (isLoading || !analyticsData?.data?.summary) {
		return (
			<div className="mt-[60px] flex w-full justify-center pr-[64px]">
				<Loader size={24} />
			</div>
		);
	}

	return (
		<div className="mt-8 grid grid-cols-1 gap-8">
			{/* Main Statistics Section */}
			<div>
				<h2 className="mb-6 text-xl font-semibold text-gray-900">
					Statistics
				</h2>
				<div
					className="grid grid-cols-1 gap-8 xl:grid-cols-2"
					// ref={(el) => ((ref as any).current[0] = el)}
				>
					{/* Visits Overview by Location */}
					{shouldDisplayChart(
						analyticsData.data.statics.location_visit_summary.data,
						["no_show", "cancelled", "walk_in", "scheduled"]
					) && (
						<div className="min-h-[500px]">
							<BarChartCustom
								title="Visits Overview by Location"
								description="Total Visits:"
								descriptionValue={Math.round(
									analyticsData.data.statics.location_visit_summary.total || 0
								)}
								data={analyticsData.data.statics.location_visit_summary.data}
								config={configs.visits}
								barKeys={[
									"no_show",
									"cancelled",
									"walk_in",
									"scheduled",
								]}
							/>
						</div>
					)}

					{/* Time Spent by Location */}
					{shouldDisplayChart(
						analyticsData.data.statics.location_time_spent_summary.data,
						["serving", "waiting"]
					) && (
						<div className="min-h-[500px]">
							<BarChartCustom
								title="Time Spent by Location"
								description="Average Time:"
								descriptionValue={
									analyticsData.data.statics.location_time_spent_summary.total_average ?? 0
								}
								data={analyticsData.data.statics.location_time_spent_summary.data}
								config={configs.time}
								barKeys={["serving", "waiting"]}
							/>
						</div>
					)}

					{/* Visits by Service Type */}
					{shouldDisplayChart(
						analyticsData.data.statics.service_visit_summary.data,
						["no_show", "scheduled", "cancelled", "walk_in"]
					) && (
						<div className="min-h-[500px]">
							<BarChartCustom
								title="Visits by Service Type"
								description="Total Service Visits:"
								descriptionValue={Math.round(
									analyticsData.data.statics.service_visit_summary.total || 0
								)}
								data={analyticsData.data.statics.service_visit_summary.data}
								config={configs.visits}
								barKeys={[
									"no_show",
									"scheduled",
									"cancelled",
									"walk_in",
								]}
							/>
						</div>
					)}

					{/* Time Spent by Service */}
					{shouldDisplayChart(
						analyticsData.data.statics.service_time_spent_summary.data,
						["serving", "waiting"]
					) && (
						<div className="min-h-[500px]">
							<BarChartCustom
								title="Time Spent by Service"
								description="Average Time Spent:"
								descriptionValue={
									Math.round(
										analyticsData.data.statics.service_time_spent_summary.total_average || 0
									) + " mins"
								}
								data={analyticsData.data.statics.service_time_spent_summary.data}
								config={configs.time}
								barKeys={["serving", "waiting"]}
							/>
						</div>
					)}

					{/* Visits by Client Category */}
					{shouldDisplayChart(
						analyticsData.data.statics.category_visit_summary.data,
						["no_show", "scheduled", "cancelled", "walk_in"]
					) && (
						<div className="min-h-[500px]">
							<BarChartCustom
								title="Visits by Client Category"
								description="Total Category Visits:"
								descriptionValue={Math.round(
									analyticsData.data.statics.category_visit_summary.total || 0
								)}
								data={analyticsData.data.statics.category_visit_summary.data}
								config={configs.visits}
								barKeys={[
									"no_show",
									"scheduled",
									"cancelled",
									"walk_in",
								]}
							/>
						</div>
					)}

					{/* Time Spent by Category */}
					{shouldDisplayChart(
						analyticsData.data.statics.category_time_spent_summary.data,
						["serving", "waiting"]
					) && (
						<div className="min-h-[500px]">
							<BarChartCustom
								title="Time Spent by Category"
								description="Average Time Spent:"
								descriptionValue={
									Math.round(
										analyticsData.data.statics.category_time_spent_summary.total_average || 0
									) + " mins"
								}
								data={analyticsData.data.statics.category_time_spent_summary.data}
								config={configs.time}
								barKeys={["serving", "waiting"]}
							/>
						</div>
					)}

					{/* Service Usage by Client Category */}
					{shouldDisplayChart(
						analyticsData.data.statics.service_usage_by_client_category.data,
						analyticsData.data.meta_data?.categories as string[]
					) && (
						<div className="min-h-[500px]">
							<BarChartCustom
								title="Service Usage by Client Category"
								description="Total Visits (Service x Category):"
								descriptionValue={Math.round(
									analyticsData.data.statics.service_usage_by_client_category.total || 0
								)}
								data={analyticsData.data.statics.service_usage_by_client_category.data}
								config={generateCategories(
									analyticsData.data.meta_data?.categories as string[]
								)}
								barKeys={
									analyticsData.data.meta_data?.categories as string[]
								}
							/>
						</div>
					)}

					{/* Station Visits by Client Category */}
					{shouldDisplayChart(
						analyticsData.data.statics.station_usage_by_client_category.data,
						analyticsData.data.meta_data?.categories as string[]
					) && (
						<div className="min-h-[500px]">
							<BarChartCustom
								title="Station Visits by Client Category"
								description="Total Visits (Service x Category):"
								descriptionValue={Math.round(
									analyticsData.data.statics.station_usage_by_client_category.total || 0
								)}
								data={analyticsData.data.statics.station_usage_by_client_category.data}
								config={generateCategories(
									analyticsData.data.meta_data?.categories as string[]
								)}
								barKeys={
									analyticsData.data.meta_data?.categories as string[]
								}
							/>
						</div>
					)}

					{/* Location Visits by Service Type */}
					{shouldDisplayChart(
						analyticsData.data.statics.location_visits_by_service_type.data,
						analyticsData.data.meta_data?.services as string[]
					) && (
						<div className="min-h-[500px]">
							<BarChartCustom
								title="Location Visits by Service Type"
								description="Total Visits (Location x Service):"
								descriptionValue={Math.round(
									analyticsData.data.statics.location_visits_by_service_type.total || 0
								)}
								data={analyticsData.data.statics.location_visits_by_service_type.data}
								config={generateCategories(
									analyticsData.data.meta_data?.services as string[]
								)}
								barKeys={
									analyticsData.data.meta_data?.services as string[]
								}
							/>
						</div>
					)}

					{/* Station Visits by Service Type */}
					{shouldDisplayChart(
						analyticsData.data.statics.station_visits_by_service_type.data,
						analyticsData.data.meta_data?.services as string[]
					) && (
						<div className="min-h-[500px]">
							<BarChartCustom
								title="Station Visits by Service Type"
								description="Total Visits (Station x Service):"
								descriptionValue={Math.round(
									analyticsData.data.statics.station_visits_by_service_type.total || 0
								)}
								data={analyticsData.data.statics.station_visits_by_service_type.data}
								config={generateCategories(
									analyticsData.data.meta_data?.services as string[]
								)}
								barKeys={
									analyticsData.data.meta_data?.services as string[]
								}
							/>
						</div>
					)}
				</div>
			</div>

			{/* Trends Section */}
			<div>
				<h2 className="trends-section mb-6 text-xl font-semibold text-gray-900">
					Trends
				</h2>
				<div
					className="grid grid-cols-1 gap-8 xl:grid-cols-2"
					// ref={(el) => ((ref as any).current[5] = el)}
				>
					{/* Weekly Visit Trends by Location */}
					{shouldDisplayChart(
						analyticsData.data.trending.weekly_visit_trend_by_location.data,
						analyticsData.data.meta_data?.status as string[]
					) && (
						<StatisticsCard
							title="Weekly Visit Trends by Location"
							description="Average Visits per Day:"
							descriptionValue={Math.round(
								analyticsData.data.trending.weekly_visit_trend_by_location.average || 0
							)}
							data={analyticsData.data.trending.weekly_visit_trend_by_location.data}
							config={generateCategories(
								analyticsData.data.meta_data?.status as string[]
							)}
							formatXAxisType="day"
							barKeys={
								analyticsData.data.meta_data?.status as string[]
							}
						/>
					)}

					{/* Hourly Visit Trends by Location */}
					{shouldDisplayChart(
						analyticsData.data.trending.hourly_visit_trend_by_location.data,
						analyticsData.data.meta_data?.status as string[]
					) && (
						<StatisticsCard
							title="Hourly Visit Trends by Location"
							description="Peak Hour:"
							descriptionValue={
								analyticsData.data.trending.hourly_visit_trend_by_location.peak_hour
							}
							subDescription="Average Visits :"
							subDescriptionValue={Math.round(
								analyticsData.data.trending.hourly_visit_trend_by_location.peak_hour_average || 0
							)}
							data={analyticsData.data.trending.hourly_visit_trend_by_location.data}
							formatXAxisType="hour"
							config={generateCategories(
								analyticsData.data.meta_data?.status as string[]
							)}
							barKeys={
								analyticsData.data.meta_data?.status as string[]
							}
						/>
					)}

					{/* Weekly Visit Trends by Service Type */}
					{shouldDisplayChart(
						analyticsData.data.trending.weekly_visit_trend_by_service.data,
						analyticsData.data.meta_data?.services as string[]
					) && (
						<StatisticsCard
							title="Weekly Visit Trends by Service Type"
							description="Most Used Service:"
							descriptionValue={`${analyticsData.data.trending.weekly_visit_trend_by_service.most_used_service} (Avg. ${Math.round(analyticsData.data.trending.weekly_visit_trend_by_service.most_used_service_average || 0)}/week)`}
							data={analyticsData.data.trending.weekly_visit_trend_by_service.data}
							config={generateCategories(
								analyticsData.data.meta_data?.services as string[]
							)}
							formatXAxisType="day"
							barKeys={
								analyticsData.data.meta_data?.services as string[]
							}
						/>
					)}

					{/* Hourly Visit Trends by Service Type */}
					{shouldDisplayChart(
						analyticsData.data.trending.hourly_visit_trend_by_service.data,
						analyticsData.data.meta_data?.services as string[]
					) && (
						<StatisticsCard
							title="Hourly Visit Trends by Service Type"
							description="Peak Hour: "
							descriptionValue={`${analyticsData.data.trending.hourly_visit_trend_by_service.peak_hour} - ${analyticsData.data.trending.hourly_visit_trend_by_service.most_used_service_in_peak_hour}`}
							data={analyticsData.data.trending.hourly_visit_trend_by_service.data}
							config={generateCategories(
								analyticsData.data.meta_data?.services as string[]
							)}
							formatXAxisType="hour"
							barKeys={
								analyticsData.data.meta_data?.services as string[]
							}
						/>
					)}
					{shouldDisplayChart(
						analyticsData.data.trending.weekly_visit_trend_by_category.data,
						analyticsData.data.meta_data?.categories as string[]
					) && (
						<StatisticsCard
							title="Weekly Visit Trends by Client Category"
							description="Top Category:"
							descriptionValue={`${analyticsData.data.trending.weekly_visit_trend_by_category.most_used_category} (Avg. ${Math.round(analyticsData.data.trending.weekly_visit_trend_by_category.most_used_category_average || 0)}/week)`}
							data={analyticsData.data.trending.weekly_visit_trend_by_category.data}
							config={generateCategories(
								analyticsData.data.meta_data?.categories as string[]
							)}
							formatXAxisType="day"
							barKeys={
								analyticsData.data.meta_data?.categories as string[]
							}
						/>
					)}

					{shouldDisplayChart(
						analyticsData.data.trending.hourly_visit_trend_by_category.data,
						analyticsData.data.meta_data?.categories as string[]
					) && (
						<StatisticsCard
							title="Hourly Visit Trends by Client Category"
							description="Category Peak Hour:"
							descriptionValue={`${analyticsData.data.trending.hourly_visit_trend_by_category.peak_hour} Elderly: ${analyticsData.data.trending.hourly_visit_trend_by_category.most_used_category_in_peak_hour}`}
							data={analyticsData.data.trending.hourly_visit_trend_by_category.data}
							config={generateCategories(
								analyticsData.data.meta_data?.categories as string[]
							)}
							formatXAxisType="hour"
							barKeys={
								analyticsData.data.meta_data?.categories as string[]
							}
						/>
					)}
				</div>
			</div>
			<div className="mt-6">
				<h2 className="mb-6 text-xl font-semibold text-gray-900">
					Ratings
				</h2>
				<div>
					<div
						className="grid grid-cols-1 gap-8 xl:grid-cols-2"
						// ref={(el) => ((ref as any).current[8] = el)}
					>
						<RatingsChart
							title="Average Ratings by Location"
							averageRating={Math.round(
								analyticsData.data.rating.rating_by_location.summary.average || 0
							)}
							participation={
								analyticsData.data.rating.rating_by_location.summary.participant_percent
							}
							totalReviews={
								analyticsData.data.rating.rating_by_location.summary.count
							}
							ratingCounts={[
								{
									rating: "5 ★",
									count:
										analyticsData.data.rating?.rating_by_location.summary
											?.distribution["5"] ?? 0,
								},
								{
									rating: "4 ★",
									count:
										analyticsData.data.rating?.rating_by_location.summary
											?.distribution["4"] ?? 0,
								},
								{
									rating: "3 ★",
									count:
										analyticsData.data.rating?.rating_by_location.summary
											?.distribution["3"] ?? 0,
								},
								{
									rating: "2 ★",
									count:
										analyticsData.data.rating?.rating_by_location.summary
											?.distribution["2"] ?? 0,
								},
								{
									rating: "1 ★",
									count:
										analyticsData.data.rating?.rating_by_location.summary
											?.distribution["1"] ?? 0,
								},
							]}
						/>
						<RatingsChart
							title="Average Ratings by Station"
							averageRating={Math.round(
								analyticsData.data.rating.rating_by_station.summary.average || 0
							)}
							participation={
								analyticsData.data.rating.rating_by_station.summary.participant_percent
							}
							totalReviews={
								analyticsData.data.rating.rating_by_station.summary.count
							}
							ratingCounts={[
								{
									rating: "5 ★",
									count:
										analyticsData.data.rating?.rating_by_station.summary
											?.distribution["5"] ?? 0,
								},
								{
									rating: "4 ★",
									count:
										analyticsData.data.rating?.rating_by_station.summary
											?.distribution["4"] ?? 0,
								},
								{
									rating: "3 ★",
									count:
										analyticsData.data.rating?.rating_by_station.summary
											?.distribution["3"] ?? 0,
								},
								{
									rating: "2 ★",
									count:
										analyticsData.data.rating?.rating_by_station.summary
											?.distribution["2"] ?? 0,
								},
								{
									rating: "1 ★",
									count:
										analyticsData.data.rating?.rating_by_station.summary
											?.distribution["1"] ?? 0,
								},
							]}
						/>

						<RatingsChart
							title="Average Ratings by Service Type"
							averageRating={Math.round(
								analyticsData.data.rating.rating_by_service.summary.average || 0
							)}
							participation={
								analyticsData.data.rating.rating_by_service.summary.participant_percent
							}
							totalReviews={
								analyticsData.data.rating.rating_by_service.summary.count
							}
							ratingCounts={[
								{
									rating: "5 ★",
									count:
										analyticsData.data.rating?.rating_by_service.summary
											?.distribution["5"] ?? 0,
								},
								{
									rating: "4 ★",
									count:
										analyticsData.data.rating?.rating_by_service.summary
											?.distribution["4"] ?? 0,
								},
								{
									rating: "3 ★",
									count:
										analyticsData.data.rating?.rating_by_service.summary
											?.distribution["3"] ?? 0,
								},
								{
									rating: "2 ★",
									count:
										analyticsData.data.rating?.rating_by_service.summary
											?.distribution["2"] ?? 0,
								},
								{
									rating: "1 ★",
									count:
										analyticsData.data.rating?.rating_by_service.summary
											?.distribution["1"] ?? 0,
								},
							]}
						/>
						<RatingsChart
							title="Average Ratings by Client Category"
							averageRating={Math.round(
								analyticsData.data.rating.rating_by_category.summary.average || 0
							)}
							participation={
								analyticsData.data.rating.rating_by_category.summary.participant_percent
							}
							totalReviews={
								analyticsData.data.rating.rating_by_category.summary.count
							}
							ratingCounts={[
								{
									rating: "5 ★",
									count:
										analyticsData.data.rating?.rating_by_category.summary
											?.distribution["5"] ?? 0,
								},
								{
									rating: "4 ★",
									count:
										analyticsData.data.rating?.rating_by_category.summary
											?.distribution["4"] ?? 0,
								},
								{
									rating: "3 ★",
									count:
										analyticsData.data.rating?.rating_by_category.summary
											?.distribution["3"] ?? 0,
								},
								{
									rating: "2 ★",
									count:
										analyticsData.data.rating?.rating_by_category.summary
											?.distribution["2"] ?? 0,
								},
								{
									rating: "1 ★",
									count:
										analyticsData.data.rating?.rating_by_category.summary
											?.distribution["1"] ?? 0,
								},
							]}
						/>
					</div>
				</div>
			</div>
		</div>
	);
});

StatisticsCharts.displayName = "StatisticsCharts";

export default StatisticsCharts;

const shouldDisplayChart = (
	summaryArray: Record<string, number>[],
	barKeys: string[]
) => {
	return (summaryArray ?? [])?.some((summary) => {
		// if (!Array.isArray(barKeys))
		// 	return Object.values(barKeys).some((key: any) => summary[key] > 0);

		return barKeys?.some((key) => summary[key] > 0);
	});
};

const configs = {
	visits: {
		scheduled: { label: "Scheduled", color: "#005893" },
		walk_in: { label: "Walk-in", color: "#D6E4EE" },
		cancelled: { label: "Cancelled", color: "#D8424B" },
		no_show: { label: "No Shows", color: "#979797" },
	},
	time: {
		serving: { label: "Serving", color: "#1E8A7C" },
		waiting: { label: "Waiting", color: "#E3E8E7" },
	},
	services: {
		service1: { label: "Service 1", color: "#637FC7" },
		service2: { label: "Service 2", color: "#23ACB6" },
		service3: { label: "Service 3", color: "#A142D8" },
		service4: { label: "Service 4", color: "#89B7EE" },
	},
	// categories: generateCategories(service_usage_by_client_category.categoryKeys),
	trends: {
		Total: { label: "Total", color: "#005893" },
		Scheduled: { label: "Scheduled", color: "#2A9D90" },
		"Walk-In": { label: "Walk-in", color: "#2F5DE5" },
		Cancelled: { label: "Cancelled", color: "#D8424B" },
		"No Shows": { label: "No Shows", color: "#979797" },
	},
	trendsServices: {
		service1: { label: "Service 1", color: "#009307" },
		service2: { label: "Service 2", color: "#689EC5" },
		service3: { label: "Service 3", color: "#64BE72" },
		service4: { label: "Service 4", color: "#348AF2" },
	},
};
