import React, { useRef, useState } from "react";
import { Maximize2 } from "lucide-react";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import {
	ChartContainer,
	ChartTooltip,
	ChartTooltipContent,
} from "@/components/ui/chart";
import {
	Line,
	LineChart,
	XAxis,
	ResponsiveContainer,
	AreaChart,
	CartesianGrid,
	Area,
} from "recharts";
import { UnExpandIcon } from "./UnexpandIcon";
import CustomLegend from "./CustomLegend";
import CustomXAxisTick from "./CustomXAxisTick";

interface ConfigItem {
	label: string;
	color: string;
}

interface Config {
	[key: string]: ConfigItem;
}

interface StatisticsCardProps {
	title: string;
	description: string;
	descriptionValue: any;
	subDescription?: string;
	subDescriptionValue?: string | number;
	data: any[];
	config: Config;
	barKeys: string[];
	formatXAxisType?: "day" | "hour";
}

const StatisticsCard: React.FC<StatisticsCardProps> = ({
	title,
	description,
	descriptionValue,
	subDescription = "",
	subDescriptionValue = "",
	data,
	config,
	barKeys,
	formatXAxisType,
}) => {
	const [isModalOpen, setIsModalOpen] = useState(false);
	const containerRef = useRef<HTMLDivElement>(null);
	const containerReff = useRef<any>(null);

	const renderChart = () => {
		// Calculate width based on data length - adjust multiplier as needed
		const chartWidth = Math.max(data.length * 80, 500);
		return (
			<div className="no-scrollbar flex flex-1 flex-col items-stretch overflow-x-auto">
				<div className="flex-1">
					<AreaChart
						width={chartWidth}
						height={350}
						data={data}
						margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
					>
						<CartesianGrid horizontal={false} vertical={false} />
						<XAxis
							dataKey="name"
							tickLine={false}
							axisLine={false}
							tickMargin={10}
							interval={0}
							tick={(props) => (
								<CustomXAxisTick
									{...props}
									payload={{
										...props.payload,
										value:
											formatXAxisType === "day"
												? props.payload.value.slice(
														0,
														3
													)
												: formatXAxisType === "hour"
													? +props.payload.value.slice(
															0,
															2
														) +
														props.payload.value
															.slice(-2)
															.toLowerCase()
													: props.payload.value,
									}}
									dataLength={data.length}
								/>
							)}
						/>
						<ChartTooltip
							cursor={false}
							content={<ChartTooltipContent />}
						/>
						{barKeys.map((key, index) => {
							const configValue = Object.values(config)[
								index
							] as ConfigItem;
							return (
								<Area
									key={key}
									dataKey={key}
									connectNulls
									type="natural"
									fill={"transparent"}
									stroke={configValue?.color}
									strokeWidth={2}
									dot={{
										fill: configValue?.color,
										strokeWidth: 5,
										r: 1,
										stroke: configValue?.color,
									}}
									stackId="a"
								/>
							);
						})}
					</AreaChart>
				</div>
			</div>
		);
	};

	const renderDialogChart = () => {
		return (
			<ResponsiveContainer width="100%" height="100%">
				<AreaChart
					accessibilityLayer
					data={data} // Use your existing data
					margin={{
						top: 20,
						right: window.innerWidth < 640 ? 20 : 30,
						left: window.innerWidth < 640 ? 20 : 30,
						bottom: 20,
					}}
				>
					<CartesianGrid horizontal={false} vertical={false} />
					<XAxis
						dataKey="name"
						tickLine={false}
						axisLine={false}
						tickMargin={10}
						interval={0}
						tick={(props) => (
							<CustomXAxisTick
								{...props}
								payload={{
									...props.payload,
									value:
										formatXAxisType === "day"
											? props.payload.value.slice(0, 3)
											: formatXAxisType === "hour"
												? +props.payload.value.slice(
														0,
														2
													) +
													props.payload.value
														.slice(-2)
														.toLowerCase()
												: props.payload.value,
								}}
								dataLength={data.length}
							/>
						)}
					/>
					<ChartTooltip
						cursor={false}
						content={<ChartTooltipContent />}
					/>
					{barKeys.map((key, index) => {
						const configValue = Object.values(config)[
							index
						] as ConfigItem;
						return (
							<Area
								key={key}
								dataKey={key}
								connectNulls
								type="natural" // Makes the chart curvy
								fill={"transparent"}
								// fillOpacity={0.4} // Adjust opacity for area fill
								stroke={configValue?.color}
								strokeWidth={2}
								dot={{
									fill: configValue?.color,
									strokeWidth: 5,
									r: 1, // Dot radius
									stroke: configValue?.color,
								}}
								stackId="a" // Stacks areas if multiple keys are used
							/>
						);
					})}
				</AreaChart>
			</ResponsiveContainer>
		);
	};

	return (
		<>
			<Card
				className="chart-container statistics-chart-container h-full"
				ref={containerReff}
			>
				<CardHeader className="relative">
					<CardTitle className="text-base text-[#09090B]">
						{title}
					</CardTitle>

					<div className="flex">
						<CardDescription className="text-xs text-[#596574]">
							{description}
							<span className="font-semibold text-[#09090B]">
								{descriptionValue}
							</span>
						</CardDescription>

						<CardDescription className="ml-3 text-xs text-[#596574]">
							{subDescription}
							<span className="font-semibold text-[#09090B]">
								{subDescriptionValue}
							</span>
						</CardDescription>
					</div>

					<div className="flex justify-end">
						<CustomLegend
							containerRef={containerReff}
							items={Object.entries(config).map(([_, value]) => ({
								label: value.label,
								color: value.color,
							}))}
						/>
					</div>
					<button
						onClick={() => setIsModalOpen(true)}
						className="absolute right-[20px] top-[15px] text-gray-500 transition-colors hover:text-gray-700"
					>
						<Maximize2 className="h-4 w-4" />
					</button>
				</CardHeader>
				<CardContent className="flex min-h-[200px] flex-1 flex-col">
					<ChartContainer config={config} className="flex-1">
						{renderChart()}
					</ChartContainer>
				</CardContent>
			</Card>

			<Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
				<DialogContent className="max-w-[90vw] md:max-w-4xl">
					<button
						onClick={() => setIsModalOpen(false)}
						className="absolute right-4 top-4 text-gray-500 transition-colors hover:text-gray-700 md:right-6 md:top-6"
					>
						<UnExpandIcon />
					</button>
					<DialogHeader className="flex-col flex-wrap items-start justify-between space-y-2 md:flex-row md:space-y-0">
						<div>
							<DialogTitle className="text-base md:text-lg">
								{title}
							</DialogTitle>
							<CardDescription className="text-xs text-[#596574] md:text-sm">
								{description}
								<b className="text-[#09090B]">
									{descriptionValue}
								</b>
							</CardDescription>
						</div>
						<div className="mr-0 w-full md:mr-10 md:w-auto">
							<CustomLegend
								isFullScreen={true}
								items={Object.entries(config).map(
									([_, value]) => ({
										label: value.label,
										color: value.color,
									})
								)}
							/>
						</div>
					</DialogHeader>
					<div
						className="h-[300px] w-full md:h-[500px]"
						ref={containerRef}
					>
						<ChartContainer config={config}>
							{renderDialogChart()}
						</ChartContainer>
					</div>
				</DialogContent>
			</Dialog>
		</>
	);
};

export default React.memo(StatisticsCard);
