import clsx from "clsx";
import { useForm, type UseFormReturn } from "react-hook-form";
import { Minus, Plus, X } from "lucide-react";
import { Button } from "@/components/ui/Button/Button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { FaPlusCircle } from "react-icons/fa";
import { DefineCheckSchema, type DefineCheckSchemaType } from "../schema/define-check";
import type { EditorCanvasCardMetaType, EditorNode } from "../libs/type";
import { useState, useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";

interface DefineCheckProps {
    open: boolean;
    onClose: () => void;
    onNext: () => void;
    onUpdateMetadata: (metadata: EditorCanvasCardMetaType) => void;
    selectedNode: EditorNode;
}

const DefineCheck = ({ open, onClose, onNext, onUpdateMetadata, selectedNode }: DefineCheckProps) => {
    const [fullyOpen, setFullyOpen] = useState(false);

    const defineCheckForm = useForm<DefineCheckSchemaType>({
        resolver: zodResolver(DefineCheckSchema)
    })

    useEffect(() => {
        if (selectedNode.data.metadata.type === "Check") {
            defineCheckForm.reset({
                checkItem: selectedNode.data?.metadata?.checkItem || '',
                action: selectedNode.data?.metadata?.selectAction || '',
                formName: selectedNode.data?.metadata?.formName || '',
                periodType: selectedNode.data?.metadata?.periodType || 'dynamic',
                numberOfChecks: selectedNode.data?.metadata?.numberOfChecks || 1,
                intervalUnit: selectedNode.data?.metadata?.intervalUnit || 'hours',
                ifMatch: selectedNode.data?.metadata?.ifMatch || [""],
                elseMatch: selectedNode.data?.metadata?.elseMatch || [""],
                checkInterval: selectedNode.data?.metadata?.checkInterval || 1,
            });
        }
    }, [selectedNode]);

    useEffect(() => {
        if (open) {
            setTimeout(() => {
                setFullyOpen(open);
            }, 10);
        }
    }, [open]);

    const handleAddAction = (key: "ifMatch" | "elseMatch") => {
        const currentValue = defineCheckForm.watch(key) || [];
        defineCheckForm.setValue(key, [...currentValue, ""]);
    };

    const onSubmit = () => {
        const updatedMetadata = {
            type: 'Check' as const,
            checkItem: defineCheckForm.watch("checkItem"),
            selectAction: defineCheckForm.watch("action"),
            numberOfChecks: defineCheckForm.watch("numberOfChecks"),
            intervalUnit: defineCheckForm.watch("intervalUnit") as "hours" | "days" | "minutes",
            checkInterval: defineCheckForm.watch("checkInterval") || 1,
            formName: defineCheckForm.watch("formName"),
            periodType: defineCheckForm.watch("periodType"),
            ifMatch: defineCheckForm.watch("ifMatch"),
            elseMatch: defineCheckForm.watch("elseMatch"),
        };

        if (onUpdateMetadata) {
            onUpdateMetadata(updatedMetadata);
        }

        onNext();
    }

    return (
        <div className={clsx("z-50 fixed top-5 right-[4%] w-[30rem] bg-white border border-[#00589340] shadow-[0px_2px_4px_-1px_#0000000F,0px_0px_6px_-1px_#0000001A] transition-transform duration-300 ease-in-out will-change-transform rounded-xl py-5", fullyOpen ? "translate-x-0" : "translate-x-[115%]")}>
            <div>

                <div className="flex items-center justify-between">
                    <h1 className="text-[#27272A] text-xl font-semibold ml-6">
                        Define Check
                    </h1>
                    <Button
                        variant="ghost"
                        className="!px-0 w-11 h-10.5 rounded-lg cursor-pointer mr-3" onClick={onClose}>
                        <X className="text-base" color="#27272A" />
                    </Button>
                </div>
                <p className="text-[#71717A] text-sm font-light ml-6">After each check condition there, you must add a action</p>
            </div>

            <div className="min-h-[78vh] max-h-[78vh] h-full flex flex-col justify-between px-5 mt-9 overflow-y-auto scrollbar-hide">
                <div className="min-h-[72vh] max-h-[72vh] overflow-y-auto flex flex-col gap-y-3">

                    <div className="flex flex-col gap-y-1.5">
                        <label htmlFor="" className="text-[#27272A] text-sm font-medium">Check Item</label>
                        <Select onValueChange={(value) => defineCheckForm.setValue(`checkItem`, value as string)} value={defineCheckForm.watch("checkItem") || ""}>
                            <SelectTrigger className="w-full bg-white">
                                <SelectValue placeholder="Select Check Item" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="email-verified">Email Verified</SelectItem>
                                <SelectItem value="phone-verified">Phone Verified</SelectItem>
                                <SelectItem value="profile-complete">Profile Complete</SelectItem>
                                <SelectItem value="has-subscription">Has Subscription</SelectItem>
                                <SelectItem value="is-admin">Is Admin</SelectItem>
                            </SelectContent>
                        </Select>
                        {defineCheckForm.formState.errors.checkItem && (
                            <p className="text-red-500 text-sm">{defineCheckForm.formState.errors.checkItem.message}</p>
                        )}
                    </div>

                    <div className="flex flex-col gap-y-1.5">
                        <label htmlFor="" className="text-[#27272A] text-sm font-medium">Select Action</label>
                        <Select
                            onValueChange={(value) => defineCheckForm.setValue(`action`, value as string)}
                            value={defineCheckForm.watch("action") || ""}
                        >
                            <SelectTrigger className="w-full bg-white">
                                <SelectValue placeholder="Select Action" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="redirect-to-registration-form">Redirect to registration form</SelectItem>
                                <SelectItem value="email">Email</SelectItem>
                                <SelectItem value="sms">SMS</SelectItem>
                                <SelectItem value="notify">Notify User</SelectItem>
                                <SelectItem value="log">Log Event</SelectItem>
                                <SelectItem value="webhook">Trigger Webhook</SelectItem>
                                <SelectItem value="update-db">Update Database</SelectItem>
                                <SelectItem value="show-modal">Show Modal</SelectItem>
                                <SelectItem value="download">Download File</SelectItem>
                                <SelectItem value="api-call">API Call</SelectItem>
                                <SelectItem value="assign-role">Assign Role</SelectItem>
                            </SelectContent>
                        </Select>
                        {defineCheckForm.formState.errors.action && (
                            <p className="text-red-500 text-sm">{defineCheckForm.formState.errors.action.message}</p>
                        )}
                    </div>

                    <div className="flex flex-col gap-y-1.5">
                        <label htmlFor="" className="text-[#27272A] text-sm font-medium">Form Name</label>
                        <Select onValueChange={(value) => defineCheckForm.setValue(`formName`, value as string)} value={defineCheckForm.watch("formName") || ""}>
                            <SelectTrigger className="w-full bg-white">
                                <SelectValue placeholder="Select Form Name" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="registration-form">Registration Form</SelectItem>
                                <SelectItem value="profile-form">Profile Form</SelectItem>
                                <SelectItem value="subscription-form">Subscription Form</SelectItem>
                                <SelectItem value="admin-form">Admin Form</SelectItem>
                            </SelectContent>
                        </Select>
                        {defineCheckForm.formState.errors.formName && (
                            <p className="text-red-500 text-sm">{defineCheckForm.formState.errors.formName.message}</p>
                        )}
                    </div>

                    <div className="flex flex-col gap-y-1.5">
                        <label htmlFor="" className="text-[#27272A] text-sm font-medium">Period Type</label>
                        <RadioGroup className="flex items-center gap-x-6 mt-0.5"
                            value={defineCheckForm.watch("periodType")}
                            onValueChange={(value) => defineCheckForm.setValue(`periodType`, value as "specific" | "dynamic")}>
                            <div className="flex items-center space-x-2">
                                <RadioGroupItem value="specific" id="specific" />
                                <Label htmlFor="specific">Specific</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                                <RadioGroupItem value="dynamic" id="dynamic" />
                                <Label htmlFor="dynamic">Dynamic</Label>
                            </div>
                        </RadioGroup>
                        {defineCheckForm.formState.errors.periodType && (
                            <p className="text-red-500 text-sm">{defineCheckForm.formState.errors.periodType.message}</p>
                        )}
                    </div>

                    <div className="flex flex-col gap-y-1.5">
                        <label htmlFor="numberOfChecks" className="text-[#27272A] text-sm font-medium">No. of Checks</label>
                        <div className="relative">
                            <Input
                                type="number"
                                id="numberOfChecks"
                                {...defineCheckForm.register("numberOfChecks", { valueAsNumber: true })}
                                className="w-full pl-4 pr-12 border border-gray-300 rounded-md text-start appearance-textfield [appearance:textfield] [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none"
                                defaultValue={1}
                                min={1}
                            />
                            <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-3">
                                <button
                                    type="button"
                                    onClick={() => {
                                        const currentValue = defineCheckForm.watch("numberOfChecks") || 1;
                                        if (currentValue > 1) {
                                            defineCheckForm.setValue("numberOfChecks", currentValue - 1);
                                        }
                                    }}
                                    className="flex items-center justify-center text-gray-700 hover:text-gray-900 text-3xl cursor-pointer size-fit"
                                >
                                    <Minus />
                                </button>
                                <div className="w-px h-5 bg-[#E4E4E7]"></div>
                                <button
                                    type="button"
                                    onClick={() => {
                                        const currentValue = defineCheckForm.watch("numberOfChecks") || 1;
                                        defineCheckForm.setValue("numberOfChecks", currentValue + 1);
                                    }}
                                    className="flex items-center justify-center text-gray-700 hover:text-gray-900 text-3xl cursor-pointer size-fit"
                                >
                                    <Plus />
                                </button>
                            </div>
                        </div>
                        {defineCheckForm.formState.errors.numberOfChecks && (
                            <p className="text-red-500 text-sm">{defineCheckForm.formState.errors.numberOfChecks.message}</p>
                        )}
                    </div>

                    <div className="flex flex-col gap-y-1.5">
                        <label htmlFor="checkInterval" className="text-[#27272A] text-sm font-medium">Check will occur every</label>
                        <div className="relative">
                            <Input
                                type="number"
                                id="checkInterval"
                                {...defineCheckForm.register("checkInterval", { valueAsNumber: true })}
                                className="w-full pl-4 pr-24 border border-gray-300 rounded-md appearance-textfield [appearance:textfield] [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none"
                                defaultValue={48}
                                min={1}
                            />
                            <Select
                                onValueChange={(value) => defineCheckForm.setValue(`intervalUnit`, value as "hours" | "days" | "minutes")}
                                value={defineCheckForm.watch("intervalUnit") || "hours"}
                            >
                                <SelectTrigger className="absolute right-2 top-1/2 transform -translate-y-1/2 w-20 border-none bg-transparent shadow-none">
                                    <SelectValue placeholder="Hours" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="hours">Hours</SelectItem>
                                    <SelectItem value="days">Days</SelectItem>
                                    <SelectItem value="minutes">Minutes</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        {defineCheckForm.formState.errors.checkInterval && (
                            <p className="text-red-500 text-sm">{defineCheckForm.formState.errors.checkInterval.message}</p>
                        )}
                    </div>

                    {(["ifMatch", "elseMatch"] as const).map((key) => (
                        <div className="bg-[#F4F4F5] py-3 px-5 rounded-xl" key={key}>
                            <h1 className="text-[#27272A] text-sm font-medium">{key === "ifMatch" ? "IF Match" : "If Not Completed Recheck Again"}</h1>

                            <div className="mt-4 flex flex-col gap-y-3">
                                {(defineCheckForm.watch(key) || []).map((action, index) => (
                                    <div key={index} className="flex flex-col gap-y-1.5">
                                        <label htmlFor={`${key}.${index}`} className="text-[#27272A] text-sm font-medium">Select Action</label>
                                        <Select onValueChange={(value) => defineCheckForm.setValue(`${key}.${index}`, value as string)} value={action || ""}>
                                            <SelectTrigger className="w-full bg-white">
                                                <SelectValue placeholder="Select Action" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="redirect-to-registration-form">Redirect to registration form</SelectItem>
                                                <SelectItem value="email">Email</SelectItem>
                                                <SelectItem value="sms">SMS</SelectItem>
                                                <SelectItem value="notify">Notify User</SelectItem>
                                                <SelectItem value="log">Log Event</SelectItem>
                                                <SelectItem value="webhook">Trigger Webhook</SelectItem>
                                                <SelectItem value="update-db">Update Database</SelectItem>
                                                <SelectItem value="show-modal">Show Modal</SelectItem>
                                                <SelectItem value="download">Download File</SelectItem>
                                                <SelectItem value="api-call">API Call</SelectItem>
                                                <SelectItem value="assign-role">Assign Role</SelectItem>
                                            </SelectContent>
                                        </Select>
                                        {defineCheckForm.formState.errors[key] && (
                                            <p className="text-red-500 text-sm">{defineCheckForm.formState.errors[key].message}</p>
                                        )}
                                    </div>
                                ))}
                            </div>

                            {(defineCheckForm.watch(key) || []).length < 3 && (
                                <button onClick={() => handleAddAction(key)} className="flex items-center gap-x-2 text-[#005893] text-sm font-medium mt-4 cursor-pointer">
                                    <FaPlusCircle /> Add Another
                                </button>
                            )}

                        </div>
                    ))}


                </div>

                <div className="flex items-center justify-end px-4">
                    <Button className="cursor-pointer" onClick={defineCheckForm.handleSubmit(onSubmit)}>
                        Add Check
                    </Button>
                </div>
            </div>
        </div>
    );
}

export default DefineCheck;