import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { DatePicker } from "@/components/common/Datepicker/DatePicker";
import {
	useBanners,
	useCreateBanner,
	useUpdateBanner,
	useDeleteBanner,
} from "@/hooks/useBanner";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import type { CreateBannerData } from "@/types/banner";

// Banner schema for validation
const bannerSchema = z
	.object({
		isEnabled: z.boolean(),
		startDate: z.date().optional(),
		endDate: z.date().optional(),
		message: z.string().max(250, "Message cannot exceed 250 characters"),
	})
	.refine(
		(data) => {
			// If banner is enabled, both dates should be provided
			if (data.isEnabled && (!data.startDate || !data.endDate)) {
				return false;
			}
			// End date should be after start date
			if (
				data.startDate &&
				data.endDate &&
				data.endDate <= data.startDate
			) {
				return false;
			}
			return true;
		},
		{
			message:
				"Please provide valid start and end dates when banner is enabled",
			path: ["endDate"],
		}
	);

type BannerFormData = z.infer<typeof bannerSchema>;

interface BannerContentProps {
	selectedLocationId?: string | null;
	selectedStationId?: string | null;
}

export const BannerContent: React.FC<BannerContentProps> = ({
	selectedLocationId,
	selectedStationId,
}) => {
	const { organizationId } = useOrganizationContext();
	const [currentBannerId, setCurrentBannerId] = useState<string | null>(null);

	// Fetch existing banners
	const { data: bannersResponse, isLoading } = useBanners({
		organizationId: organizationId || undefined,
		locationId: selectedLocationId || undefined,
		stationId: selectedStationId || undefined,
	});

	const banners = bannersResponse?.data || [];
	const currentBanner = banners.length > 0 ? banners[0] : null; // Assuming one active banner per context

	// Mutations
	const createBannerMutation = useCreateBanner();
	const updateBannerMutation = useUpdateBanner();
	const deleteBannerMutation = useDeleteBanner();

	const {
		register,
		handleSubmit,
		watch,
		setValue,
		formState: { errors, isSubmitting },
		reset,
	} = useForm<BannerFormData>({
		resolver: zodResolver(bannerSchema),
		defaultValues: {
			isEnabled: false,
			message: "",
		},
	});

	// Load existing banner data when available
	useEffect(() => {
		if (currentBanner) {
			setCurrentBannerId(currentBanner.id || null);
			reset({
				isEnabled: currentBanner.isEnabled,
				startDate: currentBanner.startDate
					? new Date(currentBanner.startDate)
					: undefined,
				endDate: currentBanner.endDate
					? new Date(currentBanner.endDate)
					: undefined,
				message: currentBanner.message,
			});
		}
	}, [currentBanner, reset]);

	const watchedValues = watch();
	const messageLength = watchedValues.message?.length || 0;
	const hasBanner = !!currentBanner;

	const onSubmit = async (data: BannerFormData) => {
		try {
			const bannerData: CreateBannerData = {
				...data,
				organizationId: organizationId || undefined,
				locationId: selectedLocationId || undefined,
				stationId: selectedStationId || undefined,
			};

			if (currentBannerId) {
				// Update existing banner
				await updateBannerMutation.mutateAsync({
					id: currentBannerId,
					...bannerData,
				});
				toast.success("Banner updated successfully");
			} else {
				// Create new banner
				const response =
					await createBannerMutation.mutateAsync(bannerData);
				setCurrentBannerId(response.data.id || null);
				toast.success("Banner created successfully");
			}
		} catch (error) {
			console.error("Error saving banner:", error);
			toast.error("Failed to save banner. Please try again.");
		}
	};

	const handleRemoveBanner = async () => {
		if (currentBannerId) {
			try {
				await deleteBannerMutation.mutateAsync(currentBannerId);
				setCurrentBannerId(null);
				reset({
					isEnabled: false,
					message: "",
					startDate: undefined,
					endDate: undefined,
				});
				toast.success("Banner removed successfully");
			} catch (error) {
				console.error("Error removing banner:", error);
				toast.error("Failed to remove banner. Please try again.");
			}
		}
	};

	const handleAddBanner = () => {
		// Just enable the form - banner will be created on submit
		setValue("isEnabled", true);
	};

	if (isLoading) {
		return (
			<div className="p-6">
				<div className="flex items-center justify-center py-8">
					<span className="text-sm text-gray-500">
						Loading banner settings...
					</span>
				</div>
			</div>
		);
	}

	return (
		<div className="max-w-2xl p-6">
			<form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
				{/* Banner Message Toggle */}
				<div className="space-y-4">
					<div className="flex items-center space-x-3">
						<Switch
							id="banner-enabled"
							checked={watchedValues.isEnabled}
							onCheckedChange={(checked) =>
								setValue("isEnabled", checked)
							}
							disabled={!hasBanner}
						/>
						<div>
							<Label
								htmlFor="banner-enabled"
								className="text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
							>
								Banner Message
							</Label>
							<p className="mt-1 text-xs text-gray-600">
								Set cancellation rules and notification
								preferences. All times are relative to the
								appointment start time.
							</p>
						</div>
					</div>

					{watchedValues.isEnabled && hasBanner && (
						<div className="ml-8 space-y-4 rounded-lg bg-gray-50 p-4">
							<div className="flex items-center space-x-2">
								<input
									type="checkbox"
									id="show-during-period"
									className="rounded border-gray-300"
									defaultChecked
								/>
								<Label
									htmlFor="show-during-period"
									className="text-sm"
								>
									Show Banner Message During the selected time
									period
								</Label>
							</div>

							{/* Date Range */}
							<div className="grid grid-cols-2 gap-4">
								<div>
									<Label className="mb-2 block text-sm font-medium">
										Start Date
									</Label>
									<DatePicker
										placeholder="Select Date"
										value={watchedValues.startDate}
										onChange={(date) =>
											setValue("startDate", date)
										}
										className="w-full"
									/>
									{errors.startDate && (
										<p className="mt-1 text-xs text-red-500">
											{errors.startDate.message}
										</p>
									)}
								</div>

								<div>
									<Label className="mb-2 block text-sm font-medium">
										End Date
									</Label>
									<DatePicker
										placeholder="Select Date"
										value={watchedValues.endDate}
										onChange={(date) =>
											setValue("endDate", date)
										}
										className="w-full"
									/>
									{errors.endDate && (
										<p className="mt-1 text-xs text-red-500">
											{errors.endDate.message}
										</p>
									)}
								</div>
							</div>

							{/* Banner Message */}
							<div>
								<Label className="mb-2 block text-sm font-medium">
									Banner Message
								</Label>
								<Textarea
									{...register("message")}
									placeholder="As you're cancelling on the same day...hours' before your selected time, charges may apply. Our billing team will get in touch with you if applicable.

You may choose to reschedule for another time. Thank you!"
									className="min-h-[120px] resize-none"
									maxLength={250}
								/>
								<div className="mt-2 flex items-center justify-between">
									<span className="text-xs text-gray-500">
										{messageLength}/250
									</span>
									{errors.message && (
										<p className="text-xs text-red-500">
											{errors.message.message}
										</p>
									)}
								</div>
							</div>
						</div>
					)}
				</div>

				{/* Action Buttons */}
				<div className="flex gap-3">
					{hasBanner ? (
						<>
							<Button
								type="button"
								variant="outline"
								onClick={handleRemoveBanner}
								disabled={deleteBannerMutation.isPending}
								className="px-6"
							>
								{deleteBannerMutation.isPending
									? "Removing..."
									: "Remove Banner"}
							</Button>
							<Button
								type="submit"
								disabled={
									isSubmitting ||
									createBannerMutation.isPending ||
									updateBannerMutation.isPending
								}
								className="px-6"
							>
								{isSubmitting ||
								createBannerMutation.isPending ||
								updateBannerMutation.isPending
									? "Saving..."
									: "Save Changes"}
							</Button>
						</>
					) : (
						<Button
							type="button"
							onClick={handleAddBanner}
							className="px-6"
						>
							Add Banner
						</Button>
					)}
				</div>
			</form>
		</div>
	);
};
