import { z } from "zod";

const ApprovedFormats = ["PNG", "PDF", "JPEG", "CSV", "Word Doc"] as const;

type UUID = `${string}-${string}-${string}-${string}-${string}`;

type FlowSubmit = {
	action: "submit";
	targetSection?: undefined;
};

type FlowContinue = {
	action: "continue";
	targetSection: UUID;
};

const createOption = (type: string) => ({
	id: crypto.randomUUID(),
	value: "",
	conditions: {
		type: "continue",
		destination: "next",
		...(type === "checkbox"
			? {
					logic: "equals",
					selected: true,
				}
			: {}),
		conditional_block_message:
			"This form submission cannot be processed at this time.",
	},
});

const baseFieldSchema = z.object({
	id: z.string(),
	title: z.string().min(1, "Field title is required"),
	description: z.string().nullable(),
	required: z.boolean().optional(),
	options: z
		.array(
			z.object({
				id: z.string(),
				value: z.string().min(1, "Option value is required"),
				conditions: z
					.object({
						type: z.enum(["continue", "submit", "goto"]),
						destination: z.string(),
						logic: z.enum(["equals", "not_equals"]).optional(),
						selected: z.boolean().optional(),
					})
					.optional(),
			})
		)
		.optional(),
});

const fieldSchemas = {
	text: baseFieldSchema.extend({
		type: z.literal("text"),
	}),

	long_text: baseFieldSchema.extend({
		type: z.literal("long_text"),
	}),

	info_image: baseFieldSchema.extend({
		type: z.literal("info_image"),
		image: z.string().min(1, "Image is required"),
		required: z.boolean().optional().default(false),
	}),

	info_text: baseFieldSchema.extend({
		type: z.literal("info_text"),
		info_text_value: z.string().min(1, "Information text is required"),
		required: z.boolean().optional().default(false),
	}),

	attachment: baseFieldSchema.extend({
		type: z.literal("attachment"),
		approved_formats: z
			.array(z.enum(["Png", "Pdf", "Jpeg", "Csv"]))
			.min(1, "At least one file format must be selected"),
	}),

	checkbox: baseFieldSchema.extend({
		type: z.literal("checkbox"),
		options: z
			.array(
				z.object({
					id: z.string(),
					value: z.string().min(1, "Option value is required"),
					conditions: z.object({
						type: z.enum(["continue", "submit", "goto"]),
						destination: z.string(),
						logic: z.enum(["equals", "not_equals"]),
						selected: z.boolean(),
					}),
				})
			)
			.min(1, "At least one option is required"),
	}),

	radio: baseFieldSchema.extend({
		type: z.literal("radio"),
		options: z
			.array(
				z.object({
					id: z.string(),
					value: z.string().min(1, "Option value is required"),
					conditions: z.object({
						type: z.enum(["continue", "submit", "goto"]),
						destination: z.string(),
					}),
				})
			)
			.min(1, "At least one option is required"),
	}),
};

const fieldOptionSchema = z.object({
	id: z.string(),
	value: z.string().min(1, "Option value is required"),
	label: z.string().min(1, "Option label is required"),
	order: z.number(),
	conditions: z
		.object({
			type: z.enum(["continue", "submit", "goto", "block"]),
			destination: z.string(),
			logic: z.enum(["equals", "not_equals"]).optional(),
			selected: z.boolean().optional(),
			conditional_block_message: z.string().nullable().optional(),
		})
		.optional(),
});

const textFieldSchema = z.object({
	id: z.string().uuid(),
	type: z.literal("text"),
	title: z.string().min(1, "Field title is required"),
	description: z.string().nullable(),
	required: z.boolean(),
	order: z.number(),
	options: z.array(fieldOptionSchema).optional(),
});

const numericFieldSchema = z.object({
	type: z.literal("numeric"),
	title: z.string().min(1, "Field title is required"),
	description: z.string().nullable(),
	required: z.boolean(),
	options: z.array(fieldOptionSchema).optional(),
});

const dateFieldSchema = z.object({
	type: z.literal("date"),
	title: z.string().min(1, "Field title is required"),
	description: z.string().nullable(),
	required: z.boolean(),
	options: z.array(fieldOptionSchema).optional(),
	dateValidation: z
		.object({
			type: z.enum(["static", "relative", "dynamic"]),
			minDate: z.string().nullable(),
			maxDate: z.string().nullable(),
			minDateOffset: z
				.object({
					value: z.number(),
					unit: z.enum(["days", "months", "years"]),
					direction: z.enum(["past", "future"]),
				})
				.optional(),
			maxDateOffset: z
				.object({
					value: z.number(),
					unit: z.enum(["days", "months", "years"]),
					direction: z.enum(["past", "future"]),
				})
				.optional(),
			minDateField: z.string().optional(),
			maxDateField: z.string().optional(),
		})
		.optional(),
});

const dateRangeFieldSchema = z.object({
	type: z.literal("date_range"),
	title: z.string().min(1, "Field title is required"),
	description: z.string().nullable(),
	required: z.boolean(),
	options: z.array(fieldOptionSchema).optional(),
	dateValidation: z
		.object({
			type: z.enum(["static", "relative", "dynamic"]),
			minDate: z.string().nullable(),
			maxDate: z.string().nullable(),
			minDateOffset: z
				.object({
					value: z.number(),
					unit: z.enum(["days", "months", "years"]),
					direction: z.enum(["past", "future"]),
				})
				.optional(),
			maxDateOffset: z
				.object({
					value: z.number(),
					unit: z.enum(["days", "months", "years"]),
					direction: z.enum(["past", "future"]),
				})
				.optional(),
			minDateField: z.string().optional(),
			maxDateField: z.string().optional(),
		})
		.optional(),
});

const longtextFieldSchema = z.object({
	type: z.literal("longtext"),
	title: z.string().min(1, "Field title is required"),
	description: z.string().nullable(),
	required: z.boolean(),
	options: z.array(fieldOptionSchema).optional(),
});

const attachmentFieldSchema = z.object({
	type: z.literal("attachment"),
	title: z.string().min(1, "Field title is required"),
	description: z.string().nullable(),
	required: z.boolean(),
	approved_formats: z
		.array(z.enum(ApprovedFormats))
		.min(1, "At least one file format must be selected"),
	options: z.array(fieldOptionSchema).optional(),
});

const infoImageFieldSchema = z.object({
	type: z.literal("info_image"),
	title: z.string().min(1, "Field title is required"),
	description: z.string().nullable(),
	image: z
		.string()
		.nullable()
		.refine((val) => {
			if (!val) return true;
			try {
				new URL(val);
				return true;
			} catch {
				return false;
			}
		}, "Invalid image URL"),
	required: z.boolean().optional().default(false),
	options: z.array(fieldOptionSchema).optional(),
});

const infoTextFieldSchema = z.object({
	type: z.literal("info_text"),
	title: z.string().min(1, "Field title is required"),
	description: z.string().nullable(),
	info_text_value: z
		.string()
		.min(1, "Information text is required")
		.nullable(),
	required: z.boolean().optional(),
	options: z.array(fieldOptionSchema).optional(),
});

const checkboxFieldSchema = z.object({
	type: z.literal("checkbox"),
	title: z.string().min(1, "Field title is required"),
	description: z.string().nullable(),
	required: z.boolean(),
	options: z
		.array(fieldOptionSchema)
		.min(1, "At least one option is required"),
});

const radioFieldSchema = z.object({
	type: z.literal("radio"),
	title: z.string().min(1, "Field title is required"),
	description: z.string().nullable(),
	required: z.boolean(),
	options: z
		.array(fieldOptionSchema)
		.min(1, "At least one option is required"),
});

const dropdownFieldSchema = z.object({
	type: z.literal("dropdown"),
	title: z.string().min(1, "Field title is required"),
	description: z.string().nullable(),
	required: z.boolean(),
	options: z
		.array(fieldOptionSchema)
		.min(1, "At least one option is required"),
});

const scaleFieldSchema = z.object({
	type: z.literal("scale_1_10"),
	title: z.string().min(1, "Field title is required"),
	description: z.string().nullable(),
	required: z.boolean(),
	options: z.array(fieldOptionSchema).optional(),
});

const scaleSatisfiedFieldSchema = z.object({
	type: z.literal("satisfaction_scale"),
	title: z.string().min(1, "Field title is required"),
	description: z.string().nullable(),
	required: z.boolean(),
	options: z.array(fieldOptionSchema).optional(),
});

const scaleAgreeFieldSchema = z.object({
	type: z.literal("agree_disagree"),
	title: z.string().min(1, "Field title is required"),
	description: z.string().nullable(),
	required: z.boolean(),
	options: z.array(fieldOptionSchema).optional(),
});

const yesNoFieldSchema = z.object({
	type: z.literal("yes_no"),
	title: z.string().min(1, "Field title is required"),
	description: z.string().nullable(),
	required: z.boolean(),
	options: z.array(fieldOptionSchema).optional(),
});

const ratingFieldSchema = z.object({
	type: z.literal("rating"),
	title: z.string().min(1, "Field title is required"),
	description: z.string().nullable(),
	required: z.boolean(),
	options: z.array(fieldOptionSchema).optional(),
});

const formFieldSchema = z
	.discriminatedUnion("type", [
		textFieldSchema,
		numericFieldSchema,
		dateFieldSchema,
		dateRangeFieldSchema,
		longtextFieldSchema,
		attachmentFieldSchema,
		infoImageFieldSchema,
		infoTextFieldSchema,
		checkboxFieldSchema,
		radioFieldSchema,
		dropdownFieldSchema,
		scaleFieldSchema,
		scaleSatisfiedFieldSchema,
		scaleAgreeFieldSchema,
		yesNoFieldSchema,
		ratingFieldSchema,
	])
	.and(
		z.object({
			id: z.string().uuid(),
			image: z.string().nullable(),
			info_text_value: z.string().nullable(),
			approved_formats: z.array(z.enum(ApprovedFormats)).optional(),
			options: z.array(fieldOptionSchema).optional(),
			order: z.number(),
		})
	);

const uuidSchema = z.custom<UUID>((val) => {
	return (
		typeof val === "string" &&
		/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
			val
		)
	);
}, "Invalid UUID format");

const sectionFlowSchema = z.discriminatedUnion("action", [
	z.object({
		action: z.literal("submit"),
		targetSection: z.undefined(),
	}) satisfies z.ZodType<FlowSubmit>,
	z.object({
		action: z.literal("continue"),
		targetSection: uuidSchema,
	}) satisfies z.ZodType<FlowContinue>,
]);

const formSectionSchema = z.object({
	id: z.string().uuid(),
	title: z.string().min(1, "Section title is required"),
	description: z.string().nullable(),
	order: z.number(),
	flow_action: z.enum(["submit", "continue", "block_form"]),
	flow_target_section_id: z.string().uuid().optional(),
	fields: z.array(formFieldSchema),
	flow: sectionFlowSchema,
});

const formBuilderSchema = z.object({
	name: z.string().min(1, "Form name is required"),
	type: z.enum(["service", "intake", "enquiry", "feedback", "referral"]),
	logo_url: z.string().nullable(),
	banner_url: z.string().nullable(),
	success_message: z.string().min(1, "Success message is required"),
	block_message: z.string().min(1, "Block message is required"),
	submit_button_title: z.string().min(1, "Button action is required"),
	service_ids: z.array(z.string()).optional(),
	location_ids: z.array(z.string()).optional(),
	station_ids: z.array(z.string()).optional(),
	apply_to: z.array(z.string()).optional(),
	status: z.enum(["live", "draft", "inactive"]),
	sections: z.array(formSectionSchema),

	collect_rating: z.boolean().optional(),
	collect_general_feedback: z.boolean().optional(),
	general_feedback_title: z.string().optional(),
	is_auto_approve: z.boolean().optional(),
});

export {
	ApprovedFormats,
	baseFieldSchema,
	fieldSchemas,
	fieldOptionSchema,
	textFieldSchema,
	numericFieldSchema,
	dateFieldSchema,
	dateRangeFieldSchema,
	longtextFieldSchema,
	attachmentFieldSchema,
	infoImageFieldSchema,
	infoTextFieldSchema,
	checkboxFieldSchema,
	radioFieldSchema,
	dropdownFieldSchema,
	scaleFieldSchema,
	scaleSatisfiedFieldSchema,
	scaleAgreeFieldSchema,
	yesNoFieldSchema,
	formFieldSchema,
	uuidSchema,
	sectionFlowSchema,
	formSectionSchema,
	formBuilderSchema,
	createOption,
};

export type { UUID, FlowSubmit, FlowContinue };
