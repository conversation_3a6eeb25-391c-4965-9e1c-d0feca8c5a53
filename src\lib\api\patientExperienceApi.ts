import { apiClient } from "./clients";

const PATIENT_EXPERIENCE_ENDPOINTS = {
	base: "/api/v1/patient-experience/feedbacks",
	stats: "/api/v1/patient-experience/stats",
	summaries: "/api/v1/patient-experience/summaries",
	detail: (responseUUId: string) =>
		`/api/v1/patient-experience/feedbacks/${responseUUId}`,
	summaryDetail: (responseUUId: string) =>
		`/api/v1/patient-experience/summaries/${responseUUId}`,
} as const;

export interface PatientExperienceFilters {
	organization_id?: number;
	location_ids?: number[];
	category_ids?: number[];
	station_ids?: number[];
	service_ids?: number[];
	page?: number;
	per_page?: number;
}

export interface PatientFeedback {
	id: string;
	patient_name: string;
	patient_avatar: string | null;
	station_name: string;
	location_name: string;
	service_name: string | null;
	rating: string | number;
	date: string;
}

export interface PatientExperienceResponse {
	success: boolean;
	data: PatientFeedback[];
	message: string;
}

export interface PatientExperienceStats {
	total_submission_count: number;
	new_submission_count: number;
	completion_rate: number;
	submit_completion_count: number;
	submit_drop_off_count: number;
	average_rating: number;
	distribution: {
		"1": number;
		"2": number;
		"3": number;
		"4": number;
		"5": number;
	};
	total_reviews: number;
}

export interface PatientExperienceStatsResponse {
	success: boolean;
	data: PatientExperienceStats;
	message: string;
}

export interface FeedbackResponse {
	field_id: number;
	question: string;
	type: string;
	display_type: string;
	raw_value: string | string[];
	formatted_value: string;
	value_type: string;
}

export interface PatientFeedbackDetail {
	question_id: string;
	question: string;
	type_label: string;
	type: string;
	station_name: string;
	location_name: string;
	service_name: string;
	total_response_count: number;
	general_feedback: string;
	responses: FeedbackResponse[];
}

export interface PatientFeedbackDetailResponse {
	success: boolean;
	data: PatientFeedbackDetail;
	message: string;
}

// New interface for the detailed feedback response structure
export interface DetailedFeedbackResponse {
	field_id: number;
	question: string;
	type: string;
	display_type: string;
	raw_value: string | string[];
	formatted_value: string;
	value_type: string;
}

export interface DetailedPatientFeedback {
	id: string;
	patient_name: string;
	patient_avatar: string | null;
	station_name: string;
	location_name: string;
	service_name: string | null;
	rating: string;
	date: string;
	general_feedback: string;
	responses: DetailedFeedbackResponse[];
}

export interface DetailedPatientFeedbackResponse {
	success: boolean;
	message: string;
	data: DetailedPatientFeedback;
}

export interface FeedbackSummary {
	question_id: string;
	question: string;
	type_label: string;
	type: string;
	station_name: string;
	location_name: string;
	service_name: string;
	total_response_count: number;
}

export interface FeedbackSummariesResponse {
	success: boolean;
	data: FeedbackSummary[];
	message: string;
}

// For ui_view: "bar" and "pie" - responses are key-value pairs
export interface SummaryDetailKeyValueResponses {
	[key: string]: number;
}

// For ui_view: "list" - responses are array of objects
export interface SummaryDetailListResponse {
	response: string;
	client_name: string;
	client_email: string;
	submitted_at: string;
}

export interface SummaryDetailData {
	question_id: string;
	question: string;
	type_label: string;
	type: string;
	station_name: string;
	location_name: string;
	service_name: string | null;
	total_response_count: number;
	ui_view: "bar" | "pie" | "list";
	responses: SummaryDetailKeyValueResponses | SummaryDetailListResponse[];
}

export interface SummaryDetailResponse {
	success: boolean;
	message: string;
	data: SummaryDetailData;
}

export const patientExperienceApi = {
	getPatientExperience: async (
		filters: PatientExperienceFilters = {}
	): Promise<PatientExperienceResponse> => {
		const params = new URLSearchParams();
		if (filters.page !== undefined) {
			params.append("page", filters.page.toString());
		}
		if (filters.per_page !== undefined) {
			params.append("per_page", filters.per_page.toString());
		}
		if (filters.location_ids && filters.location_ids.length > 0) {
			filters.location_ids.forEach((id) =>
				params.append("location_ids[]", id.toString())
			);
		}
		if (filters.category_ids && filters.category_ids.length > 0) {
			filters.category_ids.forEach((id) =>
				params.append("category_ids[]", id.toString())
			);
		}
		if (filters.station_ids && filters.station_ids.length > 0) {
			filters.station_ids.forEach((id) =>
				params.append("station_ids[]", id.toString())
			);
		}
		if (filters.service_ids && filters.service_ids.length > 0) {
			filters.service_ids.forEach((id) =>
				params.append("service_ids[]", id.toString())
			);
		}

		const queryString = params.toString();
		const url = queryString
			? `${PATIENT_EXPERIENCE_ENDPOINTS.base}?${queryString}`
			: PATIENT_EXPERIENCE_ENDPOINTS.base;

		const headers: Record<string, any> = {};
		if (filters.organization_id) {
			headers["X-organizationId"] = filters.organization_id;
		}

		const response = await apiClient.get(url, { headers });
		return response.data;
	},
	getPatientExperienceStats: async (
		organizationId: number
	): Promise<PatientExperienceStatsResponse> => {
		const response = await apiClient.get(
			PATIENT_EXPERIENCE_ENDPOINTS.stats,
			{
				headers: {
					"X-organizationId": organizationId,
				},
			}
		);
		return response.data;
	},

	getPatientFeedbackDetail: async (
		responseUUId: string,
		organizationId: number
	): Promise<DetailedPatientFeedbackResponse> => {
		const endpoint = PATIENT_EXPERIENCE_ENDPOINTS.detail(responseUUId);
		const response = await apiClient.get(endpoint, {
			headers: {
				"X-organizationId": organizationId,
			},
		});
		return response.data;
	},

	getFeedbackSummaries: async (
		filters: PatientExperienceFilters = {}
	): Promise<FeedbackSummariesResponse> => {
		const params = new URLSearchParams();
		if (filters.page !== undefined) {
			params.append("page", filters.page.toString());
		}
		if (filters.per_page !== undefined) {
			params.append("per_page", filters.per_page.toString());
		}
		if (filters.location_ids && filters.location_ids.length > 0) {
			filters.location_ids.forEach((id) =>
				params.append("location_ids[]", id.toString())
			);
		}
		if (filters.category_ids && filters.category_ids.length > 0) {
			filters.category_ids.forEach((id) =>
				params.append("category_ids[]", id.toString())
			);
		}
		if (filters.station_ids && filters.station_ids.length > 0) {
			filters.station_ids.forEach((id) =>
				params.append("station_ids[]", id.toString())
			);
		}
		if (filters.service_ids && filters.service_ids.length > 0) {
			filters.service_ids.forEach((id) =>
				params.append("service_ids[]", id.toString())
			);
		}

		const queryString = params.toString();
		const url = queryString
			? `${PATIENT_EXPERIENCE_ENDPOINTS.summaries}?${queryString}`
			: PATIENT_EXPERIENCE_ENDPOINTS.summaries;

		const headers: Record<string, any> = {};
		if (filters.organization_id) {
			headers["X-organizationId"] = filters.organization_id;
		}

		const response = await apiClient.get(url, { headers });
		return response.data;
	},

	getSummaryDetail: async (
		responseUUId: string,
		organizationId: number
	): Promise<SummaryDetailResponse> => {
		const endpoint =
			PATIENT_EXPERIENCE_ENDPOINTS.summaryDetail(responseUUId);
		const response = await apiClient.get(endpoint, {
			headers: {
				"X-organizationId": organizationId,
			},
		});
		return response.data;
	},
};
