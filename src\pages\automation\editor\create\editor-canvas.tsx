import { Button } from "@/components/ui/Button/Button";
import { cn } from "@/lib/utils";
import { FiPlusCircle } from "react-icons/fi";
import { Tooltip } from "react-tooltip";
import { PiGitForkBold } from "react-icons/pi";
import { LuMousePointerClick } from "react-icons/lu";
import { LuSearchCheck } from "react-icons/lu";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useUIStore } from "@/stores/uiStore";
import HeaderContent from "../../components/header-content";
import { useEditor } from "../../provider/editor";
import type { EditorCanvasCardMetaType, EditorCanvasCardType, EditorCanvasTypes, EditorNodeType } from "../../libs/type";
import { v4 } from "uuid";
import { toast } from "sonner";
import ReactFlow, {
    addEdge,
    applyEdgeChanges,
    applyNodeChanges,
    Background,
    MiniMap,
    ReactFlowProvider,
    useReactFlow,
    type Connection,
    type Edge,
    type EdgeChange,
    type NodeChange,
    type ReactFlowInstance,
} from "reactflow";
import 'reactflow/dist/style.css'
import { Maximize, Minus, Plus, Stethoscope } from "lucide-react";
import EditorCanvasCardSingle from "../../components/editor-canvas-card-single";
import AddActionCard from "../../actions/add-action-card";
import { CiGrid42 } from "react-icons/ci";
import AddTrigger from "../../actions/add-trigger";
import { AddTriggerSchema, type AddTriggerSchemaType } from "../../schema/add-trigger";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import AddCondition from "../../actions/add-condition";
import { DefineCheckSchema, type DefineCheckSchemaType } from "../../schema/define-check";
import DefineCheck from "../../actions/define-check";
import Testing from "../../actions/testing";
import { AddAutomationSchema, type AddAutomationSchemaType } from "../../schema/add-automation";
import AddAutomation from "../../actions/add-automation";
import DefineAction from "../../actions/define-action";

// styles for tooltp
const defaultStyles = {
    backgroundColor: "white",
    opacity: 1,
    color: "#27272A",
    boxShadow: "2px 2px 10px 0px rgba(0, 0, 0, 0.1)",
    borderRadius: "8px",
}

type OpenPanel = "add" | "condition" | "action" | "check" | "trigger" | "testing" | "add-action-card";

const description = {
    'Trigger': 'Select the trigger that start the automation.',
    'Condition': 'Select the condition that will be used to check the data.',
    'IF Match': 'Select the action that will perform if the condition is met.',
    'Else Match': 'Select the action that will perform if the condition is not met.',
    'Check': 'Select the action that will perform if the condition is not met.',
}

const Controls = () => {
    const { zoomIn, zoomOut, fitView } = useReactFlow();

    return (
        <div className="flex flex-col items-center justify-between gap-y-4">
            <div>
                <Button
                    variant='outline'
                    data-tooltip-id="zoom-in"
                    data-tooltip-content="Zoom In"
                    className='!px-0 w-11 h-10.5 rounded-lg cursor-pointer'
                    onClick={() => zoomIn()}
                >
                    <Plus size={16} color='#27272A' />
                </Button>
                <Tooltip id="zoom-in" style={defaultStyles} />
            </div>
            <div>
                <Button
                    variant='outline'
                    data-tooltip-id="zoom-out"
                    data-tooltip-content="Zoom Out"
                    className='!px-0 w-11 h-10.5 rounded-lg cursor-pointer'
                    onClick={() => zoomOut()}
                >
                    <Minus size={16} color='#27272A' />
                </Button>
                <Tooltip id="zoom-out" style={defaultStyles} />
            </div>
            <div>
                <Button
                    variant='outline'
                    data-tooltip-id="fit-view"
                    data-tooltip-content="Fit View"
                    className='!px-0 w-11 h-10.5 rounded-lg cursor-pointer'
                    onClick={() => fitView()}
                >
                    <Maximize size={16} color='#27272A' />
                </Button>
                <Tooltip id="fit-view" style={defaultStyles} />
            </div>
        </div>
    )
}

export default function EditorCanvas() {

    // hooks for editor
    const { dispatch, state } = useEditor()
    const [reactFlowInstance, setReactFlowInstance] =
        useState<ReactFlowInstance>()

    const [isActive, setIsActive] = useState(false);

    // hooks for react flow

    const onNodesChange = useCallback(
        (changes: NodeChange[]) => {
            const updatedNodes = applyNodeChanges(changes, state.editor.elements as EditorNodeType[]).map(node => ({
                ...node,
                type: node.type as EditorCanvasTypes,
            })) as EditorNodeType[];
            dispatch({
                type: 'LOAD_DATA',
                payload: {
                    elements: updatedNodes,
                    edges: state.editor.edges,
                },
            });
        },
        [state.editor.elements, state.editor.edges, dispatch]
    );

    const onEdgesChange = useCallback(
        (changes: EdgeChange[]) => {
            const updatedEdges = applyEdgeChanges(changes, state.editor.edges);
            dispatch({
                type: 'LOAD_DATA',
                payload: {
                    elements: state.editor.elements,
                    edges: updatedEdges,
                },
            });
        },
        [state.editor.elements, state.editor.edges, dispatch]
    );

    const nodeTypes = useMemo(
        () => ({
            Trigger: EditorCanvasCardSingle,
            Condition: EditorCanvasCardSingle,
            'IF Match': EditorCanvasCardSingle,
            'Else Match': EditorCanvasCardSingle,
            Check: EditorCanvasCardSingle,
        }),
        []
    )

    const onConnect = useCallback(
        (params: Edge | Connection) => {
            const newEdges = addEdge(params, state.editor.edges);
            dispatch({
                type: 'LOAD_DATA',
                payload: {
                    elements: state.editor.elements,
                    edges: newEdges,
                },
            });
        },
        [state.editor.elements, state.editor.edges, dispatch]
    );

    const onDrop = useCallback(
        (event: any) => {
            event.preventDefault()

            const type: EditorCanvasCardType['type'] = event.dataTransfer.getData(
                'application/reactflow'
            )

            // check if the dropped element is valid
            if (typeof type === 'undefined' || !type) {
                return
            }

            const triggerAlreadyExists = state.editor.elements.find(
                (node) => node.type === 'Trigger'
            )

            if (type === 'Trigger' && triggerAlreadyExists) {
                toast('Only one trigger can be added to automations at the moment')
                return
            }

            // reactFlowInstance.project was renamed to reactFlowInstance.screenToFlowPosition
            // and you don't need to subtract the reactFlowBounds.left/top anymore
            // details: https://reactflow.dev/whats-new/2023-11-10
            if (!reactFlowInstance) return
            const position = reactFlowInstance.screenToFlowPosition({
                x: event.clientX,
                y: event.clientY,
            })

            let metadata: EditorCanvasCardMetaType;
            if (type === 'Trigger') {
                metadata = { type, triggerType: '' };
            } else if (type === 'Condition') {
                metadata = {
                    type,
                    condition: [], // Required field
                };
            } else if (type === 'IF Match' || type === 'Else Match') {
                metadata = {
                    type,
                    action: [], // Required field
                };
            } else if (type === 'Check') {
                metadata = {
                    type,
                    checkItem: '',
                    selectAction: '',
                    formName: '',
                    periodType: 'dynamic',
                    numberOfChecks: 1,
                    ifMatch: [''],
                    elseMatch: [''],
                    intervalUnit: 'hours',
                    checkInterval: 1,
                };
            } else {
                return; // Invalid type
            }

            const newNode: EditorNodeType = {
                id: v4(),
                type,
                position,
                data: {
                    title: type,
                    description: description[type],
                    completed: false,
                    current: false,
                    metadata,
                    type,
                },
            };

            dispatch({
                type: 'LOAD_DATA',
                payload: {
                    elements: [...state.editor.elements, newNode],
                    edges: state.editor.edges,
                },
            });
        },
        [reactFlowInstance, state]
    )

    const onDragOver = useCallback((event: any) => {
        event.preventDefault()
        event.dataTransfer.dropEffect = 'move'
    }, [])

    const handleClickCanvas = () => {
        dispatch({
            type: 'SELECTED_ELEMENT',
            payload: {
                element: {
                    data: {
                        description: 'Select the trigger that start the automation.',
                        completed: false,
                        current: false,
                        metadata: {
                            type: 'Trigger',
                            triggerType: description['Trigger'],
                        },
                        title: '',
                        type: 'Trigger',
                    },
                    id: '',
                    position: { x: 0, y: 0 },
                    type: 'Trigger',
                },
            },
        })
    }

    // hooks for ui
    const setBreadcrumbs = useUIStore((state) => state.setBreadcrumbs);
    const setPhContent = useUIStore(
        (state) => state.setPageHeaderContent
    );

    // hooks for panels
    const panelKeys = ["add", "condition", "action", "check", "add-action-card"];
    const [open, setOpen] = useState<OpenPanel | null>("add-action-card");

    // Function to update node metadata
    const updateNodeMetadata = useCallback(
        (metadata: EditorCanvasCardMetaType) => {
            dispatch({
                type: 'UPDATE_NODE_METADATA',
                payload: {
                    nodeId: state.editor.selectedNode.id,
                    metadata,
                },
            });
        },
        [state.editor.selectedNode.id, dispatch]
    );

    const handlePanelClick = (key: OpenPanel) => {
        if (open === key) {
            setOpen(null);
        } else {
            setOpen(key);
        }
    }


    // hooks for form
    const triggerForm = useForm<AddTriggerSchemaType>({
        resolver: zodResolver(AddTriggerSchema),
        defaultValues: {
            triggerType: ''
        }
    })

    const defineCheckForm = useForm<DefineCheckSchemaType>({
        resolver: zodResolver(DefineCheckSchema),
        defaultValues: {
            checkItem: '',
            action: '',
            formName: '',
            periodType: 'dynamic',
            numberOfChecks: 1,
            ifMatch: [""],
            elseMatch: [""],
            intervalUnit: 'hours',
        }
    })

    const addAutomationForm = useForm<AddAutomationSchemaType>({
        resolver: zodResolver(AddAutomationSchema),
        defaultValues: {
            title: '',
            description: '',
        }
    })


    useEffect(() => {
        setBreadcrumbs([
            {
                label: "Automation",
                href: "/dashboard/automation",
                isCurrentPage: false,
            },
            {
                label: "Create Automation",
                href: "/dashboard/automation/create",
                isCurrentPage: true,
            },
        ]);
        return () => {
            setBreadcrumbs([]);
        };
    }, [setBreadcrumbs]);

    useEffect(() => {
        const headerContent = (
            <HeaderContent
                onTest={() => setOpen("testing")}
                canUndo={state.history.currentIndex > 0}
                canRedo={state.history.currentIndex < state.history.history.length - 1}
                dispatch={dispatch}
                title={addAutomationForm.watch('title') || "Untitled Automation"}
                focusTitleInput={() => {
                    setOpen("add")
                    setTimeout(() => {
                        addAutomationForm.setFocus("title")
                    }, 100)
                }}
                isActive={isActive}
                setIsActive={() => setIsActive(!isActive)}
            />
        );

        setPhContent(headerContent);

        return () => {
            setPhContent(null);
        };
    }, [setPhContent, state.editor.elements, dispatch, addAutomationForm.watch('title'), isActive]);

    useEffect(() => {
        if (state.editor.elements.length === 0) {
            const defaultTrigger: EditorNodeType = {
                id: v4(),
                type: 'Trigger',
                position: { x: 400, y: 300 },
                data: {
                    title: 'Trigger',
                    description: description['Trigger'],
                    completed: false,
                    current: true,
                    metadata: {
                        type: 'Trigger',
                        triggerType: "",
                    },
                    type: 'Trigger',
                },
            };
            dispatch({
                type: 'LOAD_DATA',
                payload: {
                    elements: [defaultTrigger],
                    edges: [],
                },
            });
        }
    }, [state.editor.elements, dispatch]);

    const onAutomateFlow = async () => {
        // Use reducer state for nodes and edges
        const nodes = state.editor.elements;
        const edges = state.editor.edges;

        // Create a map for quick node lookup
        const nodeMap = new Map(nodes.map(node => [node.id, node]))

        // Build parent-child relationships
        const childrenMap = new Map()
        nodes.forEach(node => {
            childrenMap.set(node.id, [])
        })

        edges.forEach(edge => {
            const parent = childrenMap.get(edge.source)
            if (parent) {
                parent.push({
                    targetId: edge.target,
                    edgeId: edge.id
                })
            }
        })

        // Find root nodes (nodes with no incoming edges)
        const rootNodes = nodes.filter(node =>
            !edges.some(edge => edge.target === node.id)
        )

        // Build complete tree structure
        const buildNodeTree = (nodeId: string, depth = 0): any => {
            const node = nodeMap.get(nodeId)
            if (!node) return null

            const children = childrenMap.get(nodeId) || []

            return {
                id: node.id,
                type: node.type,
                title: node.data.title,
                description: node.data.description,
                position: {
                    x: node.position.x,
                    y: node.position.y
                },
                metadata: node.data.metadata,
                completed: node.data.completed,
                current: node.data.current,
                depth: depth,
                children: children.map((child: any) => {
                    const childNode = buildNodeTree(child.targetId, depth + 1)
                    return {
                        ...childNode,
                        connection: {
                            edgeId: child.edgeId,
                            sourceId: nodeId,
                            targetId: child.targetId
                        }
                    }
                }).filter(Boolean)
            }
        }

        // Build the complete tree
        const completeTree = rootNodes.map(node => buildNodeTree(node.id))

        // Also log a simplified flow sequence
        const getFlowSequence = (treeNodes: any[]): string[] => {
            const sequence: string[] = []

            const traverse = (node: any) => {
                sequence.push(`${node.type} (${node.id})`)
                node.children.forEach(traverse)
            }

            treeNodes.forEach(traverse)
            return sequence
        }

        // edges.forEach(edge => {
        //     const sourceNode = nodeMap.get(edge.source)
        //     const targetNode = nodeMap.get(edge.target)
        // })

        return completeTree
    }

    useEffect(() => {
        if (state.editor.selectedNode.id) {
            if (state.editor.selectedNode.type === "Trigger") {
                setOpen("trigger")
            }

            if (state.editor.selectedNode.type === "Condition") {
                setOpen("condition")
            }

            if (state.editor.selectedNode.type === "IF Match" || state.editor.selectedNode.type === "Else Match") {
                setOpen("action")
            }

            if (state.editor.selectedNode.type === "Check") {
                setOpen("check")
            }
        }
    }, [state.editor.selectedNode])

    return (
        <ReactFlowProvider>
            <div className="flex">


                <div className='relative' style={{ height: '100vh', width: '100%' }}>
                    {/* actions button for opening panels */}
                    <div
                        className={cn(
                            'absolute min-h-[70vh] top-5 right-[-2%] w-[5rem] h-[15rem] z-[10000] flex flex-col items-center justify-between gap-y-5 transition-transform duration-300',
                            (panelKeys.some(key => open === key) || open === "trigger" || open === "testing") && 'translate-x-[-32rem]'
                        )}
                    >
                        <div className="flex flex-col items-center justify-between gap-y-5">
                            <span data-tooltip-id="add-action-card" data-tooltip-content="Add Action Card">
                                <Button
                                    variant='outline'
                                    className='!px-0 w-11 h-10.5 rounded-lg cursor-pointer'
                                    onClick={() => handlePanelClick("add-action-card")}
                                >
                                    <CiGrid42 className='text-base' color='#27272A' />
                                </Button>
                                <Tooltip id="add-action-card" style={defaultStyles} />
                            </span>
                            <span data-tooltip-id="add" data-tooltip-content="Add">
                                <Button
                                    variant='outline'
                                    className='!px-0 w-11 h-10.5 rounded-lg cursor-pointer'
                                    onClick={() => handlePanelClick("add")}
                                >
                                    <FiPlusCircle className='text-base' color='#27272A' />
                                </Button>
                                <Tooltip id="add" style={defaultStyles} />
                            </span>
                            <span data-tooltip-id="condition" data-tooltip-content="Add Condition">
                                <Button
                                    variant='outline'
                                    className='!px-0 w-11 h-10.5 rounded-lg cursor-pointer'
                                    onClick={() => handlePanelClick("condition")}
                                    disabled={state.editor.selectedNode.type !== "Condition"}
                                    tabIndex={-1}
                                >
                                    <PiGitForkBold className='text-base' color='#27272A' />
                                </Button>
                                <Tooltip id="condition" style={defaultStyles} />
                            </span>
                            <span data-tooltip-id="action" data-tooltip-content="Action">
                                <Button variant='outline'
                                    className='!px-0 w-11 h-10.5 rounded-lg cursor-pointer'
                                    onClick={() => handlePanelClick("action")}
                                    disabled={state.editor.selectedNode.type !== "IF Match" && state.editor.selectedNode.type !== "Else Match"}
                                    tabIndex={-1}
                                >
                                    <LuMousePointerClick className='text-base' color='#27272A' />
                                </Button>
                                <Tooltip id="action" style={defaultStyles} />
                            </span>
                            <span data-tooltip-id="check" data-tooltip-content="Check">
                                <Button
                                    variant='outline'
                                    className='!px-0 w-11 h-10.5 rounded-lg cursor-pointer'
                                    onClick={() => handlePanelClick("check")}
                                    disabled={state.editor.selectedNode.type !== "Check"}
                                    tabIndex={-1}
                                >
                                    <LuSearchCheck className='text-base' color='#27272A' />
                                </Button>
                                <Tooltip id="check" style={defaultStyles} />
                            </span>
                        </div>
                        <Controls />
                    </div>

                    {/* actions  */}

                    {open === "add-action-card" && <AddActionCard open={open === "add-action-card"} onClose={() => setOpen(null)} />}
                    {open === "add" && <AddAutomation
                        open={open === "add"}
                        onClose={() => setOpen(null)}
                        addAutomationForm={addAutomationForm}
                    />}
                    {open === "trigger" && <AddTrigger
                        open={open === "trigger"}
                        onClose={() => setOpen(null)}
                        onNext={() => { setOpen("add-action-card") }}
                        onUpdateMetadata={updateNodeMetadata}
                        triggerForm={triggerForm}
                    />}
                    {open === "condition" && <AddCondition
                        open={open === "condition"}
                        onClose={() => setOpen(null)}
                        onNext={() => { }}
                        onUpdateMetadata={updateNodeMetadata}
                        selectedNode={state.editor.selectedNode}
                    />}
                    {open === "check" && <DefineCheck
                        selectedNode={state.editor.selectedNode}
                        open={open === "check"}
                        onClose={() => setOpen(null)}
                        onNext={() => { }}
                        onUpdateMetadata={updateNodeMetadata}
                    />}
                    {open === "testing" && <Testing
                        open={open === "testing"}
                        onClose={() => setOpen(null)}
                    />}
                    {open === "action" && <DefineAction
                        open={open === "action"}
                        onClose={() => setOpen(null)}
                        selectedNode={state.editor.selectedNode}
                        onNext={() => { }}
                        onUpdateMetadata={updateNodeMetadata}
                    />}
                    {/* editor canvas */}
                    <ReactFlow
                        className="w-[300px]"
                        onDrop={isActive ? onDrop : () => { }}
                        onDragOver={isActive ? onDragOver : () => { }}
                        nodes={state.editor.elements}
                        onNodesChange={onNodesChange}
                        edges={state.editor.edges}
                        onEdgesChange={onEdgesChange}
                        onConnect={isActive ? onConnect : () => { }}
                        onInit={setReactFlowInstance}
                        defaultViewport={{ x: 0, y: 0, zoom: 0.9 }}
                        onClick={handleClickCanvas}
                        nodeTypes={nodeTypes}
                    >
                        <MiniMap
                            position="bottom-left"
                            className="!bg-background"
                            zoomable
                            pannable
                        />
                        <Background
                            //@ts-ignore
                            variant="dots"
                            gap={12}
                            size={1}
                        />
                    </ReactFlow>
                </div>
            </div>
        </ReactFlowProvider>
    );
}