import { useState } from "react";
import { User<PERSON><PERSON>, X } from "lucide-react";
import { Label } from "@/components/ui/label";

import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
	Sheet,
	<PERSON><PERSON><PERSON>onte<PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>er,
} from "@/components/ui/sheet";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, TabsContent } from "@/components/common/Tabs";
import { MultiAsyncSelect } from "@/components/common/MultiAsyncSelect";
import { ExpirationSettings } from "@/components/common/ExpirationSettings";
import { Input } from "@/components/ui/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

import { FormTypes } from "../types";

export interface SendFormLinkSheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	formsData: FormTypes.FormTypes[];
}

export function SendFormLinkSheet({
	open,
	onOpenChange,
	formsData,
}: SendFormLinkSheetProps) {
	const [activeTab, setActiveTab] = useState("organization");
	const [selectedPatients, setSelectedPatients] = useState<string[]>([
		"dr-samuel-johnson",
		"dr-alice-benson",
	]);
	const [selectedServices, setSelectedServices] = useState<string[]>(["all"]);
	const [message, setMessage] = useState("");
	const [sendViaSMS, setSendViaSMS] = useState(true);
	const [sendViaEmail, setSendViaEmail] = useState(true);
	const [expirationDays, setExpirationDays] = useState("5");
	const [expirationUnit, setExpirationUnit] = useState("days");
	const [useSpecificDate, setUseSpecificDate] = useState(false);
	const [selectedLocations, setSelectedLocations] = useState<string[]>([
		"all",
	]);
	const [selectedProviders, setSelectedProviders] = useState<string[]>([
		"all",
	]);
	const [showAddNewPatient, setShowAddNewPatient] = useState(false);

	// Mock data for patients
	const patientOptions = [
		{ value: "dr-samuel-johnson", label: "Dr. Samuel Johnson" },
		{ value: "dr-alice-benson", label: "Dr. Alice Benson" },
		{ value: "dr-john-doe", label: "Dr. John Doe" },
		{ value: "dr-jane-smith", label: "Dr. Jane Smith" },
	];

	const handleSendLink = () => {
		console.log("Sending booking link...", {
			tab: activeTab,
			patients: selectedPatients,
			services: selectedServices,
			message,
			sendViaSMS,
			sendViaEmail,
			expirationDays,
			useSpecificDate,
		});
		onOpenChange(false);
	};

	const handleNewPatient = () => {
		setShowAddNewPatient(true);
	};

	return (
		<Sheet open={open} onOpenChange={onOpenChange}>
			<SheetContent className="z-[1003] flex w-[520px] flex-col p-6 sm:max-w-[520px]">
				<SheetHeader className="p-0">
					<SheetTitle className="text-xl font-semibold tracking-tight text-gray-800">
						Send Form
					</SheetTitle>
				</SheetHeader>

				<div className="flex-1 overflow-y-auto">
					<div className="space-y-3.5">
						<div className="mx-0 py-2 text-xs">
							{/* Organization Tab */}
							<div className="mt-1 space-y-3.5 px-0.5">
								{/* Patients Section */}
								<div className="space-y-2">
									<label className="text-xs font-medium text-zinc-900">
										Patients
									</label>
									<MultiAsyncSelect
										options={patientOptions}
										onValueChange={setSelectedPatients}
										defaultValue={selectedPatients}
										placeholder="Select patients"
										maxCount={2}
										className="mt-1.5 w-full"
									/>
									<Button
										variant="ghost"
										size="sm"
										onClick={handleNewPatient}
										className="text-primary hover:text-primary/80 h-9 justify-start px-0"
									>
										<UserPlus className="mr-2 h-4 w-4" />
										New Patient
									</Button>
								</div>

								{/* Select Form Section */}
								<div className="space-y-2">
									<label className="text-xs font-medium text-zinc-900">
										Select Form
									</label>
									<MultiAsyncSelect
										options={formsData?.map((form) => {
											return {
												value: form.id,
												label: form.name,
											};
										}) || []}
										onValueChange={setSelectedServices}
										defaultValue={selectedServices}
										placeholder="Select services"
										className="mt-1.5 w-full"
									/>
								</div>

								{/* Message Section */}
								<div className="mt-5 space-y-2">
									<label className="text-sm font-medium tracking-tight text-gray-800">
										Message
									</label>
									<Textarea
										value={message}
										onChange={(e) =>
											setMessage(e.target.value)
										}
										placeholder=""
										className="mt-1.5 min-h-[114px] resize-none"
									/>
								</div>

								{/* Send Via Section */}
								<div className="mt-5 space-y-4">
									<div className="flex items-center gap-6">
										<span className="text-sm font-medium tracking-tight text-[#3b5566]">
											Send via
										</span>
										<div className="mt-1.5 flex items-center gap-6">
											<div className="flex items-center gap-3">
												<Checkbox
													id="sms"
													checked={sendViaSMS}
													onCheckedChange={(
														checked
													) =>
														setSendViaSMS(
															checked ===
																"indeterminate"
																? false
																: checked
														)
													}
													className="data-[state=checked]:border-primary data-[state=checked]:bg-primary"
												/>
												<label
													htmlFor="sms"
													className="text-sm font-medium text-zinc-900"
												>
													SMS
												</label>
											</div>
											<div className="flex items-center gap-3">
												<Checkbox
													id="email"
													checked={sendViaEmail}
													onCheckedChange={(
														checked
													) =>
														setSendViaEmail(
															checked ===
																"indeterminate"
																? false
																: checked
														)
													}
													className="data-[state=checked]:border-primary data-[state=checked]:bg-primary"
												/>
												<label
													htmlFor="email"
													className="text-sm font-medium text-zinc-900"
												>
													Email
												</label>
											</div>
										</div>
									</div>

									{/* Expiration Section */}
									<div className="space-y-4">
										<span className="mt-1.5 text-base font-medium tracking-tight text-gray-800">
											Form Expiry
										</span>
										<div className="mx-7.5 mt-4 flex items-start justify-between">
											<RadioGroup className="flex flex-col gap-4">
												{[
													{
														value: "duration",
														label: "Duration",
													},
													{
														value: "specific-date",
														label: "Specific Date",
													},
													{
														value: "does_not_expire",
														label: "Does Not Expire",
													},
												].map((method) => (
													<div
														className="flex items-center gap-3"
														key={method.value}
													>
														<RadioGroupItem
															value={method.value}
															id={method.value}
														/>
														<Label
															htmlFor={
																method.value
															}
														>
															{method.label}
														</Label>
													</div>
												))}
											</RadioGroup>
											<div className="flex w-[199px] flex-col items-start gap-3">
												<div className="flex items-center gap-2 rounded-lg border border-zinc-200">
													<Input
														type="number"
														value={expirationDays}
														onChange={(e) =>
															setExpirationDays(
																e.target.value
															)
														}
														className="h-9 border-none text-xs hover:border-none focus-visible:border-none focus-visible:ring-0"
													/>
													<Select defaultValue="days">
														<SelectTrigger className="h-9 w-auto min-w-[80px] border-none text-xs hover:border-none focus-visible:border-none">
															<SelectValue />
														</SelectTrigger>
														<SelectContent>
															<SelectItem value="days">
																Days
															</SelectItem>
															<SelectItem value="hours">
																Hours
															</SelectItem>
															<SelectItem value="weeks">
																Weeks
															</SelectItem>
														</SelectContent>
													</Select>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				{/* Sheet Actions */}
				<SheetFooter className="px-0 py-6">
					<div className="flex w-full cursor-pointer justify-end gap-3">
						<Button
							variant="outline"
							onClick={() => onOpenChange(false)}
							className="px-5 py-3"
						>
							Cancel
						</Button>
						<Button
							onClick={handleSendLink}
							className="bg-primary hover:bg-primary/90 cursor-pointer px-8 py-3"
						>
							Send Link
						</Button>
					</div>
				</SheetFooter>
			</SheetContent>
		</Sheet>
	);
}
