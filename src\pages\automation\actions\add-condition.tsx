import { Button } from "@/components/ui/Button/Button";
import { cn } from "@/lib/utils";
import { X } from "lucide-react";
import type { EditorCanvasCardMetaType, EditorNode } from "../libs/type";
import { Checkbox } from "@/components/common/Checkbox";
import { RefactorMultiSelect } from "@/pages/schedules/components/custom-select";
import { conditionCategories } from "../dummy-data";
import { useForm, type UseFormReturn } from "react-hook-form";
import { AddConditionSchema, type AddConditionSchemaType } from "../schema/add-condition";
import { useState, useCallback, useEffect } from "react";
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DatePicker } from "@/components/common/Datepicker/DatePicker";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { zodResolver } from "@hookform/resolvers/zod";

const conditions: string[] = [
    "Category",
    "Date & Time",
    "Registration",
    "Provider",
    "Service",
    "Location",
    "Appointment Status"
]

type AddConditionProps = {
    open: boolean;
    onClose?: () => void;
    onUpdateMetadata?: (metadata: EditorCanvasCardMetaType) => void;
    onNext: () => void;
    selectedNode: EditorNode;
}

const Step2Component = ({ conditionForm }: { conditionForm: UseFormReturn<AddConditionSchemaType> }) => {
    return (
        <div className="flex flex-col gap-y-5">
            {conditionForm.watch("condition")?.includes("Category") && (
                <div className="flex flex-col gap-y-1.5">
                    <label htmlFor="category" className="text-[#18181B] text-base">Select Categories</label>
                    <RefactorMultiSelect
                        value={conditionForm.watch("category") || []}
                        setValue={(value) => conditionForm.setValue("category", value as string[])}
                        placeholder="Select Categories"
                        label="Category"
                        id="category"
                        options={conditionCategories}
                    />
                    {conditionForm.formState.errors.category && (
                        <p className="text-red-500 text-sm">{conditionForm.formState.errors.category.message}</p>
                    )}
                </div>
            )}
            {conditionForm.watch("condition")?.includes("Date & Time") && (
                <div className="flex flex-col gap-y-1.5">
                    <label htmlFor="dateTime" className="text-[#18181B] text-base">Date Check</label>
                    <div className="flex flex-col gap-y-3">
                        <div className="grid grid-cols-2 items-center gap-x-2 gap-y-4">
                            <Select
                                value="before"
                                onValueChange={() => { }}
                            >
                                <SelectTrigger className="w-full py-5">
                                    <SelectValue placeholder="Select Type" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectGroup>
                                        <SelectItem value="before">Before</SelectItem>
                                    </SelectGroup>
                                </SelectContent>
                            </Select>

                            <DatePicker
                                onChange={(date) => {
                                    conditionForm.setValue("dateTime", {
                                        ...conditionForm.watch("dateTime"),
                                        before: date as Date,
                                    })
                                }}
                                value={conditionForm.watch("dateTime")?.before}
                                placeholder="Pick a date"
                                size="md"
                                variant="default"
                                className="w-full py-5"
                            />

                        </div>
                        <div className="grid grid-cols-2 items-center gap-x-2 gap-y-4">
                            <Select
                                value="after"
                                onValueChange={() => { }}
                            >
                                <SelectTrigger className="w-full py-5">
                                    <SelectValue placeholder="Select Type" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectGroup>
                                        <SelectItem value="after">After</SelectItem>
                                    </SelectGroup>
                                </SelectContent>
                            </Select>

                            <DatePicker
                                onChange={(date) => {
                                    conditionForm.setValue("dateTime", {
                                        ...conditionForm.watch("dateTime"),
                                        after: date as Date
                                    })
                                }}
                                value={conditionForm.watch("dateTime")?.after}
                                placeholder="Pick a date"
                                size="md"
                                variant="default"
                                className="w-full py-5"
                            />

                        </div>
                    </div>
                    {conditionForm.formState.errors.dateTime && (
                        <p className="text-red-500 text-sm">{conditionForm.formState.errors.dateTime.message}</p>
                    )}
                </div>
            )}
            {conditionForm.watch("condition")?.includes("Registration") && (
                <div className="flex flex-col gap-y-1.5">
                    <label htmlFor="" className="text-[#27272A] text-sm font-medium">Period Type</label>
                    <RadioGroup className="flex items-center gap-x-3 mt-0.5"
                        value={conditionForm.watch("registration")}
                        onValueChange={(value) => conditionForm.setValue(`registration`, value as "registered" | "not_registered" | "all")}>
                        <div className="flex items-center space-x-2">
                            <RadioGroupItem value="registered" id="registered" />
                            <Label htmlFor="registered">Registered</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                            <RadioGroupItem value="not_registered" id="not_registered" />
                            <Label htmlFor="not_registered">Not Registered</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                            <RadioGroupItem value="all" id="all" />
                            <Label htmlFor="all">All</Label>
                        </div>
                    </RadioGroup>
                    {conditionForm.formState.errors.registration && (
                        <p className="text-red-500 text-sm">{conditionForm.formState.errors.registration.message}</p>
                    )}
                </div>
            )}
            {conditionForm.watch("condition")?.includes("Provider") && (
                <div className="flex flex-col gap-y-1.5">
                    <label htmlFor="location" className="text-[#18181B] text-base">Select Stations</label>
                    <RefactorMultiSelect
                        value={conditionForm.watch("provider") || []}
                        setValue={(value) => conditionForm.setValue("provider", value as string[])}
                        placeholder="Select Stations"
                        label="Station"
                        id="station"
                        options={conditionCategories}
                    />
                    {conditionForm.formState.errors.provider && (
                        <p className="text-red-500 text-sm">{conditionForm.formState.errors.provider.message}</p>
                    )}
                </div>
            )}
        </div>
    )
}

export default function AddCondition({ open, onClose, onUpdateMetadata, selectedNode }: AddConditionProps) {
    const [currentStep, setCurrentStep] = useState(1);
    const [fullyOpen, setFullyOpen] = useState(false);

    const [selectedConditions, setSelectedConditions] = useState<string[]>([]);

    const conditionForm = useForm<AddConditionSchemaType>({
        resolver: zodResolver(AddConditionSchema),
    })

    useEffect(() => {
        if (selectedNode.data.metadata.type === "Condition") {
          conditionForm.reset({
            condition: selectedNode.data.metadata.condition || [],
            category: selectedNode.data.metadata.category || [],
            dateTime: selectedNode.data.metadata.dateTime || {},
            registration: selectedNode.data.metadata.registration || undefined,
            location: selectedNode.data.metadata.location || [],
            provider: selectedNode.data.metadata.provider || [],
            service: selectedNode.data.metadata.service || [],
            appointmentStatus: selectedNode.data.metadata.appointmentStatus || [],
          });
          setCurrentStep(1);
          setSelectedConditions(selectedNode.data.metadata?.condition || []);
        }
      }, [selectedNode]);

    useEffect(() => {
        if (open) {
            setTimeout(() => {
                setFullyOpen(open);
            }, 10);
        }
    }, [open]);

    // Map condition to form field(s) to clear
    const conditionFieldMap: Record<string, (form: UseFormReturn<AddConditionSchemaType>) => void> = {
        "Category": (form) => form.setValue("category", []),
        "Date & Time": (form) => form.setValue("dateTime", {}),
        "Registration": (form) => form.setValue("registration", undefined),
        "Location": (form) => form.setValue("location", []),
        "Provider": (form) => form.setValue("provider", []),
        "Service": (form) => form.setValue("service", []),
        "Appointment Status": (form) => form.setValue("appointmentStatus", []),
    };

    // Memoized handler for checkbox change
    const handleCheckedChange = useCallback((condition: string, checked: boolean) => {
        if (checked) {
            const newSelected = [...selectedConditions, condition];
            setSelectedConditions(newSelected);
            conditionForm.setValue("condition", [...(conditionForm.watch("condition") || []), condition]);
        } else {
            const newSelected = selectedConditions.filter((c) => c !== condition);
            setSelectedConditions(newSelected);
            conditionForm.setValue("condition", conditionForm.watch("condition")?.filter((c) => c !== condition) || []);
            // Clear the form state for this condition
            if (conditionFieldMap[condition]) {
                conditionFieldMap[condition](conditionForm);
            }
        }
        // Always update node metadata after change
        if (onUpdateMetadata) {
            const updatedMetadata = {
                type: 'Condition' as const,
                condition: conditionForm.getValues("condition"),
                category: conditionForm.getValues("category"),
                dateTime: conditionForm.getValues("dateTime"),
                registration: conditionForm.getValues("registration"),
                location: conditionForm.getValues("location"),
                provider: conditionForm.getValues("provider"),
                service: conditionForm.getValues("service"),
                appointmentStatus: conditionForm.getValues("appointmentStatus"),
            };
            onUpdateMetadata(updatedMetadata);
        }
    }, [selectedConditions, conditionForm, onUpdateMetadata]);

    const onSubmit = () => {
        const updatedMetadata = {
            type: 'Condition' as const,
            condition: conditionForm.watch("condition"),
            category: conditionForm.watch("category"),
            dateTime: conditionForm.watch("dateTime"),
            registration: conditionForm.watch("registration"),
            location: conditionForm.watch("location"),
            provider: conditionForm.watch("provider"),
            service: conditionForm.watch("service"),
            appointmentStatus: conditionForm.watch("appointmentStatus"),
        };

        if (onUpdateMetadata) {
            onUpdateMetadata(updatedMetadata);
        }
    }

    return (
        <div className={cn("z-50 fixed top-5 right-[4%] w-[30rem] bg-white border border-[#00589340] shadow-[0px_2px_4px_-1px_#0000000F,0px_0px_6px_-1px_#0000001A] transition-transform duration-300 ease-in-out will-change-transform rounded-xl py-5", fullyOpen ? "translate-x-0" : "translate-x-[115%]")}>
            <div className="flex items-center justify-between">
                <h1 className="text-[#27272A] text-xl font-semibold ml-6">Add Condition</h1>
                <Button
                    variant="ghost"
                    className="!px-0 w-11 h-10.5 rounded-lg cursor-pointer mr-3" onClick={onClose}>
                    <X className="text-base" color="#27272A" />
                </Button>
            </div>
            <div className="min-h-[78vh] max-h-[78vh] h-full flex flex-col justify-between px-5 mt-9">
                <div>
                    <div className="flex items-center justify-between gap-x-2">
                        <button onClick={() => setCurrentStep(1)} className={cn(
                            "cursor-pointer font-normal text-xs border border-[#005893] rounded-full size-6 flex items-center justify-center transition-all duration-300",
                            currentStep === 1 ? "bg-[#005893] text-white" : "bg-white text-[#005893]"
                        )}>01</button>
                        <div className="flex-1 flex items-center gap-x-4">
                            <span className={cn(
                                "font-normal text-sm transition-colors duration-300",
                                currentStep === 1 ? "text-[#005893]" : "text-[#A1A1AA]"
                            )}>Select Check Items</span>
                            <div className={cn(
                                "flex-1 h-[1.5px] transition-all duration-300",
                                currentStep === 1 ? "bg-[#E4E4E7] w-full" : "bg-[#005893] w-full"
                            )}></div>
                        </div>
                        <span className={cn(
                            "font-normal text-sm transition-colors duration-300",
                            currentStep === 2 ? "text-[#005893]" : "text-[#A1A1AA] hidden"
                        )}>Set Condition</span>
                        <button
                            onClick={() => setCurrentStep(2)}
                            disabled={selectedConditions.length === 0}
                            className={cn(
                                "cursor-pointer font-normal text-xs border border-[#005893] rounded-full size-6 flex items-center justify-center transition-all duration-300",
                                currentStep === 2 ? "bg-[#005893] text-white" : "bg-white text-[#005893]"
                            )}>02</button>
                    </div>
                    <div className="relative min-h-[65vh] max-h-[65vh] overflow-y-auto mt-9 scrollbar-hide">
                        <div className={cn(
                            "absolute inset-0 transition-transform duration-300",
                            currentStep === 1 ? "translate-x-0" : "-translate-x-full"
                        )}>
                            <div className="flex flex-col gap-y-7">
                                {conditions.map((condition) => (
                                    <label htmlFor={condition} key={condition} className="flex items-center gap-x-3 cursor-pointer">
                                        <Checkbox
                                            id={condition}
                                            checked={selectedConditions.includes(condition)}
                                            onCheckedChange={(checked) => handleCheckedChange(condition, checked as boolean)}
                                        />
                                        {condition}
                                    </label>
                                ))}
                            </div>
                        </div>
                        <div className={cn(
                            "absolute inset-0 transition-transform duration-300",
                            currentStep === 2 ? "translate-x-0" : "translate-x-full"
                        )}>
                            <Step2Component conditionForm={conditionForm} />
                        </div>
                    </div>
                </div>
                <div className="flex justify-end">
                    {currentStep === 1 && (
                        <Button className="cursor-pointer" disabled={selectedConditions.length === 0} onClick={() => setCurrentStep(2)}>
                            Continue
                        </Button>
                    )}
                    {currentStep === 2 && (
                        <div className="pt-4">
                            <Button className="cursor-pointer" onClick={conditionForm.handleSubmit(onSubmit)}>
                                Add Condition
                            </Button>
                        </div>
                    )}
                </div>
            </div>
        </div>
    )
}