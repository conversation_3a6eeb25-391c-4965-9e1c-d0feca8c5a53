import { Card, CardContent } from "@/components/ui/card";
import { FormTypes, FormResponseTypes } from "../types";
import {
	AgreeDisagreeFieldRenderer,
	AttachmentFieldR<PERSON>er,
	Checkbox<PERSON>ield<PERSON><PERSON>er,
	Date<PERSON><PERSON><PERSON><PERSON>er,
	DateR<PERSON>e<PERSON><PERSON><PERSON><PERSON><PERSON>,
	Dropdown<PERSON>ield<PERSON><PERSON><PERSON>,
	InfoTextField<PERSON>enderer,
	InfoImageFieldRenderer,
	SatisfactionRatingFieldRenderer,
	ScaleFieldRenderer,
	RadioFieldRenderer,
	TextFieldRenderer,
} from "./renderers";

interface FormRendererProps {
	formData: FormTypes.FormDataType;
	responses?: FormResponseTypes.FormResponse[];
	mode: "preview" | "view" | "edit";
	onResponseChange?: (fieldId: string, value: any) => void;
	onSubmit?: (responses: FormResponseTypes.FormResponse[]) => void;
}

// Map field types to renderer components
const fieldRendererMap: Record<string, React.FC<any>> = {
	text: TextField<PERSON><PERSON><PERSON>,
	longtext: TextField<PERSON><PERSON><PERSON>,
	numeric: TextField<PERSON><PERSON><PERSON>,
	date: DateField<PERSON><PERSON><PERSON>,
	date_range: DateR<PERSON>e<PERSON>ield<PERSON><PERSON>er,
	dropdown: Dropdown<PERSON><PERSON><PERSON><PERSON><PERSON>,
	radio: RadioFieldRenderer,
	checkbox: CheckboxFieldRenderer,
	attachment: AttachmentFieldRenderer,
	info_image: InfoImageFieldRenderer,
	info_text: InfoTextFieldRenderer,
	scale_1_10: ScaleFieldRenderer,
	satisfaction_scale: SatisfactionRatingFieldRenderer,
	agree_disagree: AgreeDisagreeFieldRenderer,
	yes_no: RadioFieldRenderer, // fallback to radio
	rating: SatisfactionRatingFieldRenderer, // fallback
};

// Helper to get the value for a field from responses
function getFieldValue(
	fieldId: string,
	responses: FormResponseTypes.FormResponse[] = []
) {
	for (const response of responses) {
		if (response.field_responses) {
			const match = response.field_responses.find(
				(r) => r.field_id === fieldId
			);
			if (match) return match.value;
		}
	}
	return undefined;
}

export const FormRenderer: React.FC<FormRendererProps> = ({
	formData,
	responses = [],
	mode,
	onResponseChange,
	onSubmit,
}) => {
	return (
		<Card className="border-t-primary border-t-4 py-0">
			<CardContent className="space-y-6 p-6">
				<div className="flex items-center justify-between">
					<h2 className="text-3xl font-light text-slate-900">
						{formData.name}
					</h2>
				</div>
				<div className="space-y-4">
					{formData.sections.map((section) => (
						<div key={section.id} className="space-y-4">
							<h2 className="text-lg font-medium">
								{section.title}
							</h2>
							<div className="space-y-4">
								{section.fields.map((field) => {
									const Renderer =
										fieldRendererMap[field.type] ||
										(() => (
											<div>
												Unsupported field type:{" "}
												{field.type}
											</div>
										));
									const value = getFieldValue(
										field.id,
										responses
									);
									return (
										<div
											key={field.id}
											className="mb-8 space-y-2.5"
										>
											<h3 className="text-sm font-medium text-[#323539]">
												{field.title}
											</h3>
											<Renderer
												field={field}
												value={value}
												mode={mode}
												onChange={
													onResponseChange
														? (val: any) =>
																onResponseChange(
																	field.id,
																	val
																)
														: undefined
												}
											/>
										</div>
									);
								})}
							</div>
						</div>
					))}
				</div>
			</CardContent>
		</Card>
	);
};
