import { useEffect, useState, type FC, useMemo } from "react";
import { useUIStore } from "@/stores/uiStore";
import { Inbox, Plus, Search, Settings2, X } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { InputText } from "@/components/common/InputText";
import { FormsFilterSheet, SendFormLinkSheet } from "./sheets";
import { FormResponsesCard, FormResponsesHeader } from "./components";
import { useNavigate, useSearchParams } from "react-router";
import * as Types from "./types";
import { Tabs, TabsContent } from "@/components/common/Tabs";
import {
	useGetForms,
	useGetFormCounts,
	useDeleteForm,
} from "./store/slices/formSlice";
import { useDebounce } from "@/hooks/useDebounce";
import { Badge } from "@/components/ui/badge";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import { useModal } from "@/lib/hooks/useModal";

// Map tab values to API type parameters
const getApiTypeParam = (tabValue: string): string | undefined => {
	if (tabValue === "all") return undefined; // Don't send type param for "all"
	if (tabValue === "general") return "general"; // API expects "general" not "general-inquiry"
	return tabValue; // "intake", "service", "feedback" map directly
};

// Filter data interface to match what FormsFilterSheet provides
interface FilterData {
	location_ids: string[];
	station_ids: string[];
	service_ids: string[];
	type: string[];
}

export const FormResponsesPage: FC = () => {
	const { organizationId } = useOrganizationContext();
	const setBreadcrumbs = useUIStore((state) => state.setBreadcrumbs);
	const setCurrentPageTitle = useUIStore(
		(state) => state.setCurrentPageTitle
	);
	const navigate = useNavigate();
	const [searchParams] = useSearchParams();
	const { openModal, closeModal, activeModal, updateModalData } = useModal();

	const [searchTerm, setSearchTerm] = useState("");
	const debouncedSearchTerm = useDebounce(searchTerm, 300);
	const [showFilterSheet, setShowFilterSheet] = useState(false);
	const [showSendFormLinkSheet, setShowSendFormLinkSheet] = useState(false);
	const [selectedForms, setSelectedForms] = useState<string[]>([]);
	const [currentPage, setCurrentPage] = useState(1);
	// Get current active tab from URL params
	const activeTab = searchParams.get("form-type-tab") || "all";
	const [appliedFilters, setAppliedFilters] = useState<FilterData>({
		location_ids: [],
		station_ids: [],
		service_ids: [],
		type: [],
	});

	// Transform filters to API parameters
	const apiParams = useMemo(() => {
		const params: Types.FormTypes.GetFormsQueryParams = {
			page: currentPage.toString(),
			per_page: "10",
		};

		// Set type based on active tab
		const tabType = getApiTypeParam(activeTab);
		if (tabType) {
			params.type = tabType;
		}

		// Add search term if available
		if (debouncedSearchTerm) {
			params.search = debouncedSearchTerm;
		}

		// Force the query to include filter parameters even if empty
		// This ensures React Query sees parameter changes
		if (appliedFilters.location_ids.length > 0) {
			params.location_ids = appliedFilters.location_ids
				.map((id) => id.toString())
				.join(",");
		}

		if (appliedFilters.station_ids.length > 0) {
			params.station_ids = appliedFilters.station_ids
				.map((id) => id.toString())
				.join(",");
		}

		if (appliedFilters.service_ids.length > 0) {
			params.service_ids = appliedFilters.service_ids
				.map((id) => id.toString())
				.join(",");
		}

		if (appliedFilters.type.length > 0) {
			params.type = appliedFilters.type.join(",");
		}

		return params;
	}, [
		currentPage,
		activeTab,
		debouncedSearchTerm,
		appliedFilters.location_ids,
		appliedFilters.station_ids,
		appliedFilters.service_ids,
		appliedFilters.type,
	]);

	// Get forms data for the active tab with applied filters
	const {
		data: formsData,
		isLoading,
		refetch: refetchForms,
	} = useGetForms({
		params: apiParams,
		organizationId: organizationId!,
	});

	const { mutate: deleteForm, isPending: isDeletingForm } = useDeleteForm();

	// Get counts for all form types with single optimized API call
	const { data: formResponseCounts } = useGetFormCounts({
		organizationId: organizationId!,
	});

	// Create dynamic tab items with counts
	const formTypeTabs = useMemo(
		() => [
			{
				value: "all",
				label: "All",
				count: formResponseCounts?.all || null,
			},
			{
				value: "pending",
				label: "Pending",
				count: formResponseCounts?.intake || null,
			},
			{
				value: "approved",
				label: "Approved",
				count: formResponseCounts?.service || null,
			},
			{
				value: "declined",
				label: "Declined",
				count: formResponseCounts?.general || null,
			},
		],
		[formResponseCounts]
	);

	// Filter forms by search term on client-side (if API doesn't support search)
	const filteredForms = useMemo(() => {
		if (!formsData?.data) return [];

		let filtered = formsData.data;

		// Apply form type filter on client-side if multiple types are selected
		// or if we're not on the "all" tab and have additional form type filters
		if (
			appliedFilters.type.length > 0 &&
			(activeTab !== "all" || appliedFilters.type.length > 1)
		) {
			filtered = filtered.filter((form: any) =>
				appliedFilters.type.includes(form.type)
			);
		}

		// Apply search filter on client-side if API doesn't handle it
		if (debouncedSearchTerm && !apiParams.search) {
			filtered = filtered.filter(
				(form: any) =>
					form.name
						.toLowerCase()
						.includes(debouncedSearchTerm.toLowerCase()) ||
					form.description
						?.toLowerCase()
						.includes(debouncedSearchTerm.toLowerCase())
			);
		}

		return filtered;
	}, [formsData?.data, appliedFilters, debouncedSearchTerm, activeTab]);

	// Reset page when tab changes or filters are applied
	useEffect(() => {
		setCurrentPage(1);
	}, [activeTab, appliedFilters]);

	// Reset selected forms when tab changes
	useEffect(() => {
		setSelectedForms([]);
	}, [activeTab]);

	const handleSelectAll = (checked: boolean) => {
		if (checked && filteredForms) {
			setSelectedForms(filteredForms.map((form: any) => form.id));
		} else {
			setSelectedForms([]);
		}
	};

	const handleFormSelection = (formId: string, selected: boolean) => {
		if (selected) {
			setSelectedForms((prev) => [...prev, formId]);
		} else {
			setSelectedForms((prev) => prev.filter((id) => id !== formId));
		}
	};

	const handlePageChange = (page: number) => {
		setCurrentPage(page);
	};

	const handleViewForm = (form: Types.FormTypes.FormTypes) => {
		console.log("View form:", form.id);
		setShowSendFormLinkSheet(true);
	};

	// Update modal loading state when isDeletingForm changes
	useEffect(() => {
		if (activeModal === "confirmation") {
			updateModalData({ isLoading: isDeletingForm });
		}
	}, [isDeletingForm, activeModal, updateModalData]);

	const handleDeleteForm = (formId: string) => {
		openModal("confirmation", {
			size: "md",
			data: {
				title: "Delete form?",
				message:
					"Are you sure you want to delete this form? This action cannot be undone.",
				confirmText: "Yes, Delete",
				cancelText: "No",
				variant: "destructive",
				onConfirm: () => {
					deleteForm(
						{ id: formId, organizationId: organizationId! },
						{
							onSuccess: () => {
								closeModal();
								refetchForms();
							},
							onError: () => {
								// Optionally show an error toast
							},
						}
					);
				},
				onClose: () => {
					closeModal();
				},
			},
		});
	};

	// Set breadcrumbs when component mounts
	useEffect(() => {
		setBreadcrumbs([
			{
				label: "Dashboard",
				href: "/",
			},
			{
				label: "Forms",
				href: "/dashboard/forms",
			},
			{
				label: "Form Manager",
				href: "/dashboard/forms",
			},
		]);

		setCurrentPageTitle("Form Manager");

		// Cleanup breadcrumbs when component unmounts
		return () => {
			setBreadcrumbs([]);
		};
	}, [setBreadcrumbs, setCurrentPageTitle]);

	const handleApplyFilters = (filterData: FilterData) => {
		setAppliedFilters(filterData);
		setShowFilterSheet(false);
		setCurrentPage(1); // Reset to first page when filters are applied
		// The query will automatically refetch due to the improved query key
	};

	// Clear filters function for future use
	const handleClearFilters = () => {
		setAppliedFilters({
			location_ids: [],
			station_ids: [],
			service_ids: [],
			type: [],
		});
		setCurrentPage(1);
	};

	// Helper function to check if any filters are applied
	const hasActiveFilters = () => {
		return (
			appliedFilters.location_ids.length > 0 ||
			appliedFilters.station_ids.length > 0 ||
			appliedFilters.service_ids.length > 0 ||
			appliedFilters.type.length > 0
		);
	};

	// Get count of active filters
	const activeFilterCount = () => {
		let count = 0;
		if (appliedFilters.location_ids.length > 0) count++;
		if (appliedFilters.station_ids.length > 0) count++;
		if (appliedFilters.service_ids.length > 0) count++;
		if (appliedFilters.type.length > 0) count++;
		return count;
	};

	// Common table content component to avoid duplication
	const TableContent = () =>
		isLoading ? (
			<div className="py-12 text-center">
				<div className="border-primary mx-auto h-8 w-8 animate-spin rounded-full border-b-2"></div>
				<p className="mt-2 text-sm text-gray-500">Loading forms...</p>
			</div>
		) : filteredForms && filteredForms.length > 0 ? (
			<div className="grid w-full flex-1 overflow-hidden overflow-x-auto">
				<div className="grid min-w-[1000px] flex-col overflow-x-auto rounded-lg border border-zinc-200">
					<FormResponsesHeader
						selectedForms={selectedForms}
						formResponse={{
							data: filteredForms || [],
							meta: {
								pagination: {
									count:
										formsData?.meta.pagination.count || 0,
									current_page:
										formsData?.meta.pagination
											.current_page || 1,
									per_page:
										formsData?.meta.pagination.per_page ||
										10,
									total:
										formsData?.meta.pagination.total || 0,
									total_pages:
										formsData?.meta.pagination
											.total_pages || 0,
								},
							},
						}}
						handleSelectAll={handleSelectAll}
					/>

					{/* Forms Grid */}
					<div className="flex flex-col gap-0.5 whitespace-nowrap">
						{filteredForms.map((form: any) => (
							<FormResponsesCard
								key={form.id}
								form={form}
								isSelected={selectedForms.includes(form.id)}
								onSelectionChange={(selected: boolean) =>
									handleFormSelection(form.id, selected)
								}
								onEdit={() =>
									console.log("Edit form:", form.id)
								}
								onView={() => handleViewForm(form)}
								onDelete={() => handleDeleteForm(form.id)}
							/>
						))}
					</div>

					{/* Pagination */}
					{formsData?.meta.pagination.total_pages &&
						formsData.meta.pagination.total_pages > 1 && (
							<div className="mt-8 flex items-center justify-center gap-2 p-4">
								<Button
									variant="outline"
									disabled={
										formsData?.meta.pagination
											.current_page === 1
									}
									onClick={() =>
										handlePageChange(
											formsData?.meta.pagination
												.current_page - 1
										)
									}
								>
									Previous
								</Button>

								<span className="text-sm text-gray-600">
									Page{" "}
									{formsData?.meta.pagination.current_page} of{" "}
									{formsData?.meta.pagination.total_pages}
								</span>

								<Button
									variant="outline"
									disabled={
										formsData?.meta.pagination
											.current_page ===
										formsData?.meta.pagination.total_pages
									}
									onClick={() =>
										handlePageChange(
											formsData?.meta.pagination
												.current_page + 1
										)
									}
								>
									Next
								</Button>
							</div>
						)}
				</div>
			</div>
		) : (
			<div className="space-y-6 py-12 text-center">
				<Inbox className="mx-auto h-12 w-12" />
				<h3 className="mt-2 text-sm font-medium text-gray-900">
					No forms added
				</h3>
				<p className="mt-1 text-sm text-gray-500">
					{searchTerm
						? `No forms found matching "${searchTerm}"`
						: "Get started by creating your first form."}
				</p>
				{!searchTerm && (
					<Button
						className="mt-4"
						onClick={() => navigate("/dashboard/forms/create")}
					>
						<Plus className="mr-2 h-4 w-4" />
						Create a Form
					</Button>
				)}
			</div>
		);

	return (
		<div className="flex flex-col gap-4 py-6">
			{/* Header */}
			<div className="flex items-center justify-between pl-4">
				<h1 className="text-xl font-semibold">Form Responses</h1>
				<div className="flex items-center gap-3">
					<div className="relative max-w-md flex-1">
						<InputText
							placeholder="Search forms..."
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="pl-10 focus-visible:ring-0"
							id="search-field"
							variant="with-icon"
							icon={<Search className="h-4 w-4" />}
							iconPosition="left"
						/>
					</div>
					<Button
						variant="outline"
						className={`relative h-10 cursor-pointer ${
							hasActiveFilters()
								? "border-primary bg-primary/10"
								: ""
						}`}
						size="icon"
						onClick={() => setShowFilterSheet(true)}
					>
						<Settings2 className="h-4 w-4" />
						{hasActiveFilters() && (
							<Badge
								variant="secondary"
								className="bg-primary absolute -top-2 -right-2 flex h-5 w-5 items-center justify-center border-0 p-0 text-xs text-white"
							>
								{activeFilterCount()}
							</Badge>
						)}
					</Button>
					<Button
						variant="outline"
						className="bg-primary hover:bg-primary/90 h-10 cursor-pointer text-white hover:text-white"
						onClick={() => navigate("/dashboard/forms/create")}
					>
						<Plus className="mr-2 h-4 w-4" />
						Create a Form
					</Button>
				</div>
			</div>

			{/* Active Filters Indicator */}
			{hasActiveFilters() && (
				<div className="flex items-center gap-2 px-4">
					<span className="text-sm text-gray-600">
						Active filters:
					</span>
					<div className="flex flex-wrap items-center gap-2">
						{appliedFilters.location_ids.length > 0 && (
							<Badge
								variant="secondary"
								className="bg-blue-100 text-blue-800"
							>
								Locations ({appliedFilters.location_ids.length})
							</Badge>
						)}
						{appliedFilters.station_ids.length > 0 && (
							<Badge
								variant="secondary"
								className="bg-green-100 text-green-800"
							>
								Providers ({appliedFilters.station_ids.length})
							</Badge>
						)}
						{appliedFilters.service_ids.length > 0 && (
							<Badge
								variant="secondary"
								className="bg-purple-100 text-purple-800"
							>
								Services ({appliedFilters.service_ids.length})
							</Badge>
						)}
						{appliedFilters.type.length > 0 && (
							<Badge
								variant="secondary"
								className="bg-orange-100 text-orange-800"
							>
								Form Types ({appliedFilters.type.length})
							</Badge>
						)}
						<Button
							variant="ghost"
							size="sm"
							onClick={handleClearFilters}
							className="h-6 px-2 text-xs text-gray-500 hover:text-gray-700"
						>
							<X className="mr-1 h-3 w-3" />
							Clear all
						</Button>
					</div>
				</div>
			)}

			{/* Tabs */}
			<Tabs
				items={formTypeTabs}
				defaultValue="all"
				useRouting={true}
				searchParamKey="form-type-tab"
			>
				<TabsContent value="all">
					<TableContent />
				</TabsContent>
				<TabsContent value="intake">
					<TableContent />
				</TabsContent>
				<TabsContent value="service">
					<TableContent />
				</TabsContent>
				<TabsContent value="general">
					<TableContent />
				</TabsContent>
				<TabsContent value="feedback">
					<TableContent />
				</TabsContent>
			</Tabs>

			{/* Send Form Link Sheet */}
			<SendFormLinkSheet
				open={showSendFormLinkSheet}
				onOpenChange={setShowSendFormLinkSheet}
				formsData={formsData?.data || []}
			/>

			{/* Filter Sheet */}
			<FormsFilterSheet
				open={showFilterSheet}
				onOpenChange={setShowFilterSheet}
				onApplyFilters={handleApplyFilters}
			/>
		</div>
	);
};
