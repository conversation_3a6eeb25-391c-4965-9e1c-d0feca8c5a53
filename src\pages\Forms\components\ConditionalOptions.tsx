import React, { useRef, useState } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Uploader } from "@/components/common/Uploader";

import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import {
	FormField,
	FormLabel,
	FormMessage,
	FormItem,
	FormControl,
} from "@/components/ui/form";
import { Controller, useFieldArray } from "react-hook-form";
import { ArrowRight, GripVertical, Minus, X, Plus, Upload } from "lucide-react";
import { Checkbox } from "@/components/common/Checkbox";
import { HiOutlineCloudUpload } from "react-icons/hi";
import RichTextEditor from "@/components/common/RichTextEditor/Editor";

import {
	DndContext,
	DragOverlay,
	closestCenter,
	KeyboardSensor,
	PointerSensor,
	useSensor,
	useSensors,
} from "@dnd-kit/core";
import {
	SortableContext,
	sortableKeyboardCoordinates,
	verticalListSortingStrategy,
	useSortable,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { toast } from "react-hot-toast";

const UploadImageSlice = () => {
	return {
		mutate: () => {},
		isLoading: false,
	};
};

const SortableOption = ({
	option,
	optionIndex,
	nestIndex,
	field,
	control,
	watch,
	remove,
	handleAddOption,
	renderConditionPath,
	renderOptionBlockMessage,
}: {
	option: any;
	optionIndex: number;
	nestIndex: number;
	field: any;
	control: any;
	watch: any;
	remove: any;
	handleAddOption: any;
	renderConditionPath: any;
	renderOptionBlockMessage: any;
}) => {
	const { attributes, listeners, setNodeRef, transform, transition } =
		useSortable({ id: option.id });

	const style = {
		transform: CSS.Transform.toString(transform),
		transition,
	};

	return (
		<div ref={setNodeRef} style={style} className="space-y-2">
			<div className="flex items-center space-x-2">
				<div
					{...listeners}
					{...attributes}
					className="cursor-move text-gray-400"
				>
					<GripVertical size={16} className="text-[#323539]" />
				</div>
				<div className="flex flex-1 items-center gap-3">
					<div className="flex flex-1 items-center gap-2">
						<div className="flex-1">
							<Controller
								control={control}
								name={`sections.${nestIndex}.fields.${field.name}.options.${optionIndex}.value`}
								render={({ field: inputField }) => (
									<Input
										{...inputField}
										placeholder={`Option ${optionIndex + 1}`}
										className="w-full"
									/>
								)}
							/>
						</div>
						{renderConditionPath(option, optionIndex)}
						<div className="flex w-12.5 items-center gap-1">
							<Button
								type="button"
								variant="secondary"
								className="h-6 w-6 cursor-pointer p-1 text-gray-400 hover:bg-red-500 hover:text-white"
								onClick={() => remove(optionIndex)}
							>
								<X size={12} />
							</Button>
							{optionIndex ===
								(
									watch(
										`sections.${nestIndex}.fields.${field.name}.options`
									) as any[]
								).length -
									1 && (
								<Button
									type="button"
									variant="secondary"
									className="h-6 w-6 cursor-pointer p-1 text-gray-400 hover:bg-green-500 hover:text-white"
									onClick={handleAddOption}
								>
									<Plus size={12} />
								</Button>
							)}
						</div>
					</div>
				</div>
			</div>
			{renderOptionBlockMessage(option, optionIndex)}
		</div>
	);
};

const ConditionalOptions = ({
	nestIndex,
	field,
	control,
	watch,
	setValue,
}: {
	nestIndex: number;
	field: any;
	control: any;
	watch: any;
	setValue: any;
}) => {
	const fileInputRef = useRef<HTMLInputElement>(null);
	const { fields, append, remove, update, move } = useFieldArray({
		control,
		name: `sections.${nestIndex}.fields.${field.name}.options`,
	});
	const [showConditions, setShowConditions] = useState(false);
	const [activeDragId, setActiveDragId] = useState(null);
	const { mutate: uploadImage, isLoading: isUploading } = UploadImageSlice();

	const sensors = useSensors(
		useSensor(PointerSensor),
		useSensor(KeyboardSensor, {
			coordinateGetter: sortableKeyboardCoordinates,
		})
	);

	const fieldType = watch(`sections.${nestIndex}.fields.${field.name}.type`);
	const createNewOption = () => ({
		id: crypto.randomUUID(),
		value: "",
		conditions: {
			type: "continue",
			destination: "next",
			logic:
				fieldType === "checkbox" || fieldType === "dropdown"
					? "equals"
					: undefined,
			selected:
				fieldType === "checkbox" || fieldType === "dropdown"
					? true
					: undefined,
			conditional_block_message:
				"Your form submission cannot be processed at this time.",
		},
	});

	const handleAddOption = () => {
		append(createNewOption());
	};

	const handleDragStart = (event: any) => {
		setActiveDragId(event.active.id);
	};

	const handleDragEnd = (event: any) => {
		const { active, over } = event;
		setActiveDragId(null);

		if (active.id !== over.id) {
			const oldIndex = fields.findIndex(
				(field) => field.id === active.id
			);
			const newIndex = fields.findIndex((field) => field.id === over.id);
			move(oldIndex, newIndex);
		}
	};

	const getDestinationOptions = () => {
		const allSections = watch("sections");
		const currentSectionIndex = allSections.findIndex((section: any) =>
			section.fields.some((f: any) => f.id === field.value.id)
		);

		return (
			<>
				<SelectItem value="next">Continue to next section</SelectItem>
				<SelectItem value="submit">Submit form</SelectItem>
				<SelectItem value="block">Block Access and Submit</SelectItem>
				{allSections.map((section: any, index: number) => {
					if (index > currentSectionIndex) {
						return (
							<SelectItem key={section.id} value={section.id}>
								Go to Section {index + 1}
							</SelectItem>
						);
					}
					return null;
				})}
			</>
		);
	};

	const renderLogicSelector = (option: any, optionIndex: number) => {
		if (fieldType !== "checkbox" && fieldType !== "dropdown") return null;

		return (
			<Select
				value={option.conditions?.logic || "equals"}
				onValueChange={(value) => {
					const updatedOption = {
						...option,
						conditions: {
							...option.conditions,
							logic: value,
						},
					};
					update(optionIndex, updatedOption);
				}}
			>
				<SelectTrigger className="flex-1">
					<SelectValue />
				</SelectTrigger>
				<SelectContent>
					<SelectItem value="equals">Is selected</SelectItem>
					<SelectItem value="not_equals">Is not selected</SelectItem>
				</SelectContent>
			</Select>
		);
	};

	const renderOptionBlockMessage = (option: any, optionIndex: number) => {
		if (
			!showConditions ||
			fieldType === "checkbox" ||
			option.conditions.type !== "block"
		)
			return null;

		return (
			<>
				<div className="ml-8 flex-1">
					<div className="rounded-md border border-gray-200 p-4 pt-4">
						<FormField
							control={control}
							name={`sections.${nestIndex}.fields.${field.name}.options.${optionIndex}.conditions.conditional_block_message`}
							render={({ field }) => (
								<FormItem>
									<FormLabel className="text-xs font-normal italic">
										{`Option ${optionIndex + 1} Block Message on Submission`}
									</FormLabel>
									<FormControl>
										<Input
											placeholder={`"Enter here"`}
											className="bg-gray-100 text-xs shadow-sm placeholder:text-xs"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
					</div>
				</div>
			</>
		);
	};

	const renderConditionPath = (option: any, optionIndex: number) => {
		if (!showConditions || fieldType === "checkbox") return null;

		return (
			<>
				{renderLogicSelector(option, optionIndex)}
				<div className="flex-1">
					<Controller
						name={`sections.${nestIndex}.fields.${field.name}.options.${optionIndex}.conditions.destination`}
						control={control}
						render={({ field: destinationField }) => {
							const currentOptions = watch(
								`sections.${nestIndex}.fields.${field.name}.options`
							);
							const currentOption = currentOptions[optionIndex];

							return (
								<Select
									value={destinationField.value}
									onValueChange={(value) => {
										const updatedOption = {
											...currentOption,
											conditions: {
												...currentOption.conditions,
												type:
													value === "submit"
														? "submit"
														: value === "next"
															? "continue"
															: value === "block"
																? "block"
																: "goto",
												order: optionIndex + 1,
												destination: value,
												conditional_block_message:
													value === "block"
														? "Your form submission cannot be processed at this time."
														: "",
											},
										};

										// Update the form state
										update(optionIndex, updatedOption);
										destinationField.onChange(value);
									}}
								>
									<SelectTrigger className="w-full">
										<SelectValue />
									</SelectTrigger>
									<SelectContent>
										{getDestinationOptions()}
									</SelectContent>
								</Select>
							);
						}}
					/>
				</div>
			</>
		);
	};

	const renderAttachmentOptions = () => (
		<div className="mt-4 space-y-4">
			<div className="ml-5 border-t pt-4">
				<h4 className="text-xs font-medium text-[#323539]">
					Select File Types that are acceptable
				</h4>
				<FormField
					name={`sections.${nestIndex}.fields.${field.name}.approved_formats`}
					control={control}
					render={({ field: controllerField }) => {
						const currentFormats = controllerField.value || [];
						return (
							<>
								<div className="mt-3 grid max-w-[350px] grid-cols-3 gap-6">
									{[
										"PNG",
										"JPEG",
										"PDF",
										"Word Doc",
										"CSV",
									].map((format) => (
										<div
											key={format}
											className="flex items-center space-x-2"
										>
											<Checkbox
												id={format.toLowerCase()}
												className="rounded-sm border-2 border-[#D1D1D1]"
												checked={currentFormats.includes(
													format
												)}
												onCheckedChange={(checked) => {
													const newFormats =
														currentFormats.includes(
															format
														)
															? currentFormats.filter(
																	(
																		f: string
																	) =>
																		f !==
																		format
																)
															: [
																	...currentFormats,
																	format,
																];
													controllerField.onChange(
														newFormats
													);
												}}
											/>
											<Label
												className="text-xs font-medium"
												htmlFor={format.toLowerCase()}
											>
												{format}
											</Label>
										</div>
									))}
								</div>
								<FormMessage className="mt-2" />
							</>
						);
					}}
				/>
			</div>
		</div>
	);

	const renderImageOptions = (uploadImage: any, isUploading: boolean) => {
		const handleImageUpload = (
			event: React.ChangeEvent<HTMLInputElement>,
			imageControl: any
		) => {
			const file = event.target.files?.[0];
			if (!file) return;

			const MAX_FILE_SIZE_MB = 2;
			const ACCEPTED_FORMATS = ["jpg", "jpeg", "png", "gif", "svg"];
			const fileFormat = file.type.split("/")[1];

			if (file.size > MAX_FILE_SIZE_MB * 1024 * 1024) {
				imageControl.setError(
					`sections.${nestIndex}.fields.${field.name}.image`,
					{
						message: `Max file size is ${MAX_FILE_SIZE_MB}MB`,
					}
				);
				return;
			}

			if (!ACCEPTED_FORMATS.includes(fileFormat)) {
				imageControl.setError(
					`sections.${nestIndex}.fields.${field.name}.image`,
					{
						message: `${fileFormat.toUpperCase()} is not supported`,
					}
				);
				return;
			}

			// Clear any existing errors
			imageControl.setError(
				`sections.${nestIndex}.fields.${field.name}.image`,
				{
					message: "",
				}
			);

			const formData = new FormData();
			formData.append("file", file);

			uploadImage(formData, {
				onSuccess: (response: any) => {
					toast.success(
						<div className="-my-2 mr-[-20px] flex items-center space-x-2.5">
							<div className="flex items-center space-x-5">
								<p>
									{response.message ||
										"Image uploaded successfully"}
								</p>
							</div>
							<button
								className="h-fit p-2.5"
								onClick={() => toast.dismiss("upload-image")}
							>
								<i className="mgc_close_line" />
							</button>
						</div>,
						{
							id: "upload-image",
							duration: 10000,
						}
					);
					setValue(
						`sections.${nestIndex}.fields.${field.name}.image`,
						response.data.image_url
					);
				},
				onError: (error: any) => {
					imageControl.setError(
						`sections.${nestIndex}.fields.${field.name}.image`,
						{
							message:
								error?.response?.data?.message ||
								"Failed to upload image. Please try again.",
						}
					);
				},
			});
		};

		const handleRemoveImage = () => {
			setValue(`sections.${nestIndex}.fields.${field.name}.image`, "");
		};

		const currentImage = watch(
			`sections.${nestIndex}.fields.${field.name}.image`
		);

		return (
			<div className="mx-24 mt-4 space-y-4">
				<div className="pt-4">
					<>
						<FormField
							name={`sections.${nestIndex}.fields.${field.name}.image`}
							control={control}
							render={({ field: { onChange, value } }) => {
								return (
									<>
										<div className="flex flex-col gap-2">
											<Uploader
												files={[]}
												onFilesChange={() => {}}
												onFileRemove={() => {
													onChange("");
												}}
												descriptionText="Recommended file type: .svg, .png, .jpg (Max of 10 mb)"
												accept=".svg,.png,.jpg,.jpeg"
												maxFileSize={10 * 1024 * 1024}
												multiple={false}
												maxFiles={1}
												size="sm"
												uploadText="Click or drag file here to upload file"
												uploadIcon={
													<Upload className="h-4 w-4 text-black" />
												}
												enableServerUpload={true}
												onUploadSuccess={(
													_,
													url: string
												) => {
													onChange(url);
												}}
											/>
										</div>
										<FormMessage className="mt-2" />
									</>
								);
							}}
						/>
					</>
				</div>
			</div>
		);
	};

	const renderInfoTextOptions = () => (
		<div className="mt-4 space-y-4">
			<div className="border-t pt-4">
				<FormField
					name={`sections.${nestIndex}.fields.${field.name}.info_text_value`}
					control={control}
					render={() => {
						return (
							<>
								<RichTextEditor
									name={`sections.${nestIndex}.fields.${field.name}.info_text_value`}
									control={control}
									disableAutoFocus={true}
								/>
								<FormMessage className="mt-2" />
							</>
						);
					}}
				/>
			</div>
		</div>
	);

	if (fieldType === "attachment") {
		return renderAttachmentOptions();
	}

	if (fieldType === "info_image") {
		return renderImageOptions(uploadImage, isUploading);
	}

	if (fieldType === "info_text") {
		return renderInfoTextOptions();
	}

	if (
		!fieldType ||
		(fieldType !== "radio" &&
			fieldType !== "checkbox" &&
			fieldType !== "dropdown")
	) {
		return null;
	}

	return (
		<DndContext
			sensors={sensors}
			collisionDetection={closestCenter}
			onDragStart={handleDragStart}
			onDragEnd={handleDragEnd}
		>
			<div className="mt-4 space-y-4">
				<SortableContext
					items={fields.map((field) => field.id)}
					strategy={verticalListSortingStrategy}
				>
					<div className="space-y-3">
						{fields.map((option, optionIndex) => (
							<SortableOption
								key={option.id}
								option={option}
								optionIndex={optionIndex}
								nestIndex={nestIndex}
								field={field}
								control={control}
								watch={watch}
								// update={update}
								remove={remove}
								handleAddOption={handleAddOption}
								renderConditionPath={renderConditionPath}
								renderOptionBlockMessage={
									renderOptionBlockMessage
								}
								// renderLogicSelector={renderLogicSelector}
								// fieldType={fieldType}
							/>
						))}
					</div>
				</SortableContext>

				<div className="flex items-center justify-end pt-1">
					<div className="flex items-center space-x-4">
						{fieldType !== "checkbox" && (
							<Button
								type="button"
								variant="ghost"
								size="sm"
								className="flex cursor-pointer items-center space-x-2"
								onClick={() =>
									setShowConditions(!showConditions)
								}
							>
								{showConditions ? (
									<Minus
										color="#fff"
										className="rounded-full bg-red-600"
										size={12}
									/>
								) : (
									<svg
										width="14"
										height="14"
										viewBox="0 0 14 14"
										fill="none"
										xmlns="http://www.w3.org/2000/svg"
									>
										<path
											d="M4.6665 7.00033H9.33317M6.99984 4.66699V9.33366M12.8332 7.00033C12.8332 10.222 10.2215 12.8337 6.99984 12.8337C3.77818 12.8337 1.1665 10.222 1.1665 7.00033C1.1665 3.77866 3.77818 1.16699 6.99984 1.16699C10.2215 1.16699 12.8332 3.77866 12.8332 7.00033Z"
											stroke="#005893"
											stroke-width="1.5"
											stroke-linecap="round"
											stroke-linejoin="round"
										/>
									</svg>
								)}
								<span>
									{showConditions ? "Remove" : "Add"}{" "}
									Condition
								</span>
							</Button>
						)}
					</div>
				</div>

				<DragOverlay>
					{activeDragId ? (
						<div className="rounded-md border bg-white p-0">
							<Input
								value={
									(
										fields.find(
											(f) => f.id === activeDragId
										) as { id: string; value: string }
									)?.value || ""
								}
								readOnly
								className="w-full"
							/>
						</div>
					) : null}
				</DragOverlay>
			</div>
		</DndContext>
	);
};

export default ConditionalOptions;
